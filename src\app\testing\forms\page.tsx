'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/core/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import Link from 'next/link'
import {
  ArrowLeft,
  FileText,
  Play,
  CheckCircle,
  AlertCircle,
  Save,
  Send,
  RefreshCw,
  Database,
  Clock,
  Target,
  Zap
} from 'lucide-react'

type FormType = 'quick_win' | 'suggestion' | 'improvement_full'

interface TestResult {
  success: boolean
  message: string
  data?: any
  timestamp: string
}

interface DraftTestResult {
  saved: boolean
  retrieved: boolean
  data?: any
}

export default function FormsTestPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<{[key: string]: TestResult}>({})
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  const [draftResults, setDraftResults] = useState<{[key: string]: DraftTestResult}>({})
  const [testingPhase, setTestingPhase] = useState<'idle' | 'validation' | 'submission' | 'draft'>('idle')

  const { user } = useAuth()

  // بيانات اختبار لكويك وين
  const quickWinData = {
    request_number: `QW-${Date.now()}`,
    form_type: 'quick_win',
    form_data: {
      projectTitle: 'مشروع كويك وين - تحسين سرعة النظام',
      section: 'قسم تطوير النظم',
      projectExecutor: {
        name: 'مطور النظام',
        phone: '0501234567',
        email: '<EMAIL>'
      },
      problemDescription: 'بطء في استجابة النظام يؤثر على تجربة المستخدم',
      indicatorName: 'سرعة الاستجابة',
      currentValue: 5,
      targetValue: 2,
      unit: 'ثانية',
      improvementDirection: 'decrease',
      dataSource: 'مراقبة النظام',
      measurementMethod: 'قياس متوسط وقت الاستجابة',
      solution: {
        description: 'تحسين استعلامات قاعدة البيانات وتحسين الكاش',
        tasks: [
          { title: 'تحسين الاستعلامات', assignee: 'مطور قاعدة البيانات' },
          { title: 'تحسين نظام التخزين المؤقت', assignee: 'مطور الواجهة الخلفية' }
        ],
        implementationWeeks: 2,
        estimatedCost: 3000
      },
      teamLeader: {
        name: 'قائد فريق التطوير',
        phone: '0501234568',
        email: '<EMAIL>'
      }
    }
  }

  // بيانات اختبار للاقتراح
  const suggestionData = {
    request_number: `SG-${Date.now()}`,
    form_type: 'suggestion',
    form_data: {
      projectTitle: 'اقتراح تحسين - نظام إشعارات محسن',
      section: 'قسم تجربة المستخدم',
      projectExecutor: {
        name: 'مصمم التجربة',
        phone: '0501234569',
        email: '<EMAIL>'
      },
      problemDescription: 'نظام الإشعارات الحالي غير فعال ولا يجذب انتباه المستخدمين',
      indicatorName: 'معدل قراءة الإشعارات',
      currentValue: 30,
      targetValue: 80,
      unit: '%',
      improvementDirection: 'increase',
      dataSource: 'تحليلات النظام',
      measurementMethod: 'نسبة الإشعارات المقروءة إلى المرسلة'
    }
  }

  const testForm = async (formType: FormType, data: any) => {
    setLoading(true)
    setErrors({})
    
    try {
      console.log(`Testing ${formType} form with data:`, data)
      
      const response = await fetch('/api/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()
      
      if (response.ok) {
        setResults(prev => ({
          ...prev,
          [formType]: {
            success: true,
            data: result,
            timestamp: new Date().toISOString()
          }
        }))
      } else {
        setErrors(prev => ({
          ...prev,
          [formType]: result.error || 'حدث خطأ غير متوقع'
        }))
      }
    } catch (error) {
      console.error(`Error testing ${formType}:`, error)
      setErrors(prev => ({
        ...prev,
        [formType]: 'فشل في الاتصال بالخادم'
      }))
    } finally {
      setLoading(false)
    }
  }

  // دالة اختبار التحقق من صحة البيانات
  const testValidation = async (formType: FormType, testData: any) => {
    setTestingPhase('validation')

    try {
      // اختبار بيانات ناقصة
      const incompleteData = { ...testData }
      delete incompleteData.form_data.projectTitle

      const response = await fetch('/api/requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(incompleteData)
      })

      const result = await response.json()

      if (response.ok) {
        throw new Error('كان يجب أن يفشل الاختبار مع البيانات الناقصة')
      }

      setResults(prev => ({
        ...prev,
        [`${formType}_validation`]: {
          success: true,
          message: 'تم رفض البيانات الناقصة بنجاح',
          data: result,
          timestamp: new Date().toISOString()
        }
      }))

    } catch (error) {
      setErrors(prev => ({
        ...prev,
        [`${formType}_validation`]: error instanceof Error ? error.message : 'خطأ في اختبار التحقق'
      }))
    }
  }

  // دالة اختبار حفظ المسودة
  const testDraftSave = async (formType: FormType, testData: any) => {
    setTestingPhase('draft')

    try {
      if (!user?.id) {
        throw new Error('يجب تسجيل الدخول لاختبار المسودات')
      }

      // اختبار حفظ المسودة
      const draftData = {
        form_type: formType,
        form_data: testData.form_data,
        user_id: user.id
      }

      const saveResponse = await fetch('/api/requests/drafts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(draftData)
      })

      const saveResult = await saveResponse.json()

      if (!saveResponse.ok) {
        throw new Error(saveResult.error || 'فشل في حفظ المسودة')
      }

      // اختبار استرجاع المسودة
      const retrieveResponse = await fetch(`/api/requests/drafts?user_id=${user.id}&form_type=${formType}`)
      const retrieveResult = await retrieveResponse.json()

      if (!retrieveResponse.ok) {
        throw new Error('فشل في استرجاع المسودة')
      }

      setDraftResults(prev => ({
        ...prev,
        [formType]: {
          saved: true,
          retrieved: true,
          data: {
            saved: saveResult,
            retrieved: retrieveResult
          }
        }
      }))

      setResults(prev => ({
        ...prev,
        [`${formType}_draft`]: {
          success: true,
          message: 'تم حفظ واسترجاع المسودة بنجاح',
          data: { saveResult, retrieveResult },
          timestamp: new Date().toISOString()
        }
      }))

    } catch (error) {
      setErrors(prev => ({
        ...prev,
        [`${formType}_draft`]: error instanceof Error ? error.message : 'خطأ في اختبار المسودة'
      }))
    }
  }

  // دالة اختبار شاملة
  const runComprehensiveTest = async (formType: FormType, testData: any) => {
    setLoading(true)
    setErrors({})
    setResults({})
    setDraftResults({})

    try {
      // إضافة معرف المستخدم
      const dataWithUser = {
        ...testData,
        created_by: user?.id || 'test-user-id'
      }

      // 1. اختبار التحقق من صحة البيانات
      await testValidation(formType, dataWithUser)
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 2. اختبار حفظ المسودة
      await testDraftSave(formType, dataWithUser)
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 3. اختبار إرسال النموذج
      await testForm(formType, dataWithUser)

    } catch (error) {
      console.error('Comprehensive test error:', error)
    } finally {
      setLoading(false)
      setTestingPhase('idle')
    }
  }

  const getResultIcon = (formType: string) => {
    if (results[formType]?.success) {
      return <CheckCircle className="w-5 h-5 text-green-500" />
    }
    if (errors[formType]) {
      return <AlertCircle className="w-5 h-5 text-red-500" />
    }
    return null
  }

  const getTestPhaseIcon = () => {
    switch (testingPhase) {
      case 'validation':
        return <Target className="w-4 h-4 text-blue-600" />
      case 'draft':
        return <Save className="w-4 h-4 text-yellow-600" />
      case 'submission':
        return <Send className="w-4 h-4 text-green-600" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <ProtectedLayout>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          {/* Navigation */}
          <div className="mb-6">
            <Link 
              href="/testing" 
              className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              العودة إلى لوحة الاختبارات
            </Link>
          </div>

          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <FileText className="w-8 h-8 text-purple-600" />
              <h1 className="text-3xl font-bold text-gray-900">
                اختبار النماذج
              </h1>
            </div>
            <p className="text-gray-600 max-w-2xl mx-auto">
              اختبار شامل لجميع أنواع النماذج وعمليات الإرسال
            </p>
          </div>

          {/* User Info */}
          {user && (
            <Card className="mb-6 p-4 bg-blue-50 border-blue-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                  {user.name?.charAt(0) || 'U'}
                </div>
                <div>
                  <p className="font-medium text-gray-900">{user.name}</p>
                  <p className="text-sm text-gray-600">{user.email}</p>
                </div>
              </div>
            </Card>
          )}

          {/* Test Status */}
          {testingPhase !== 'idle' && (
            <Card className="mb-6 p-4 bg-yellow-50 border-yellow-200">
              <div className="flex items-center gap-3">
                {getTestPhaseIcon()}
                <div>
                  <p className="font-medium text-gray-900">
                    جاري تنفيذ الاختبارات...
                  </p>
                  <p className="text-sm text-gray-600">
                    المرحلة الحالية: {
                      testingPhase === 'validation' ? 'التحقق من صحة البيانات' :
                      testingPhase === 'draft' ? 'اختبار المسودات' :
                      testingPhase === 'submission' ? 'اختبار الإرسال' : 'جاري التحضير'
                    }
                  </p>
                </div>
              </div>
            </Card>
          )}

          {/* Comprehensive Tests */}
          <Card className="mb-6 p-6 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Zap className="w-6 h-6 text-purple-600" />
              اختبارات شاملة
            </h3>
            <p className="text-gray-600 mb-6">
              اختبار جميع مراحل النموذج: التحقق من البيانات، حفظ المسودة، والإرسال النهائي
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={() => runComprehensiveTest('quick_win', quickWinData)}
                disabled={loading}
                className="bg-orange-600 hover:bg-orange-700 flex items-center gap-2"
              >
                <Zap className="w-4 h-4" />
                اختبار شامل - كويك وين
              </Button>

              <Button
                onClick={() => runComprehensiveTest('suggestion', suggestionData)}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
              >
                <Zap className="w-4 h-4" />
                اختبار شامل - اقتراح
              </Button>
            </div>

            {/* Test Results Summary */}
            {Object.keys(results).length > 0 && (
              <div className="mt-6 p-4 bg-white rounded-lg border">
                <h4 className="font-semibold text-gray-900 mb-3">نتائج الاختبارات:</h4>
                <div className="space-y-2">
                  {Object.entries(results).map(([key, result]) => (
                    <div key={key} className="flex items-center gap-2 text-sm">
                      {result.success ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <AlertCircle className="w-4 h-4 text-red-500" />
                      )}
                      <span className="font-medium">{key}:</span>
                      <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                        {result.message || (result.success ? 'نجح' : 'فشل')}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </Card>

          {/* Individual Test Forms */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quick Win Test */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  اختبار نموذج كويك وين
                </h3>
                {getResultIcon('quick_win')}
              </div>
              
              <p className="text-gray-600 mb-4">
                اختبار نموذج مشروع التحسين السريع (كويك وين)
              </p>

              <div className="space-y-3 mb-4">
                <div className="text-sm">
                  <span className="font-medium">العنوان:</span> {quickWinData.form_data.projectTitle}
                </div>
                <div className="text-sm">
                  <span className="font-medium">القسم:</span> {quickWinData.form_data.section}
                </div>
                <div className="text-sm">
                  <span className="font-medium">المؤشر:</span> {quickWinData.form_data.indicatorName}
                </div>
              </div>

              <div className="space-y-2">
                <Button
                  onClick={() => testForm('quick_win', { ...quickWinData, created_by: user?.id })}
                  disabled={loading}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  <Send className="w-4 h-4 mr-2" />
                  اختبار الإرسال
                </Button>

                <Button
                  onClick={() => testDraftSave('quick_win', quickWinData)}
                  disabled={loading}
                  variant="secondary"
                  className="w-full"
                >
                  <Save className="w-4 h-4 mr-2" />
                  اختبار حفظ المسودة
                </Button>

                <Button
                  onClick={() => testValidation('quick_win', quickWinData)}
                  disabled={loading}
                  variant="secondary"
                  className="w-full"
                >
                  <Target className="w-4 h-4 mr-2" />
                  اختبار التحقق
                </Button>
              </div>

              {/* Results for Quick Win */}
              {errors.quick_win && (
                <ErrorMessage message={errors.quick_win} className="mt-3" />
              )}

              {results.quick_win && (
                <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 font-medium">تم الإرسال بنجاح!</p>
                  <p className="text-sm text-green-600">
                    رقم الطلب: {results.quick_win.data?.request_number}
                  </p>
                </div>
              )}

              {/* Draft Results */}
              {draftResults.quick_win && (
                <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-blue-800 font-medium">نتائج اختبار المسودة:</p>
                  <div className="text-sm text-blue-600 mt-1">
                    <div>✓ حفظ المسودة: {draftResults.quick_win.saved ? 'نجح' : 'فشل'}</div>
                    <div>✓ استرجاع المسودة: {draftResults.quick_win.retrieved ? 'نجح' : 'فشل'}</div>
                  </div>
                </div>
              )}
            </Card>

            {/* Suggestion Test */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  اختبار نموذج الاقتراح
                </h3>
                {getResultIcon('suggestion')}
              </div>
              
              <p className="text-gray-600 mb-4">
                اختبار نموذج مقترح التحسين
              </p>

              <div className="space-y-3 mb-4">
                <div className="text-sm">
                  <span className="font-medium">العنوان:</span> {suggestionData.form_data.projectTitle}
                </div>
                <div className="text-sm">
                  <span className="font-medium">القسم:</span> {suggestionData.form_data.section}
                </div>
                <div className="text-sm">
                  <span className="font-medium">المؤشر:</span> {suggestionData.form_data.indicatorName}
                </div>
              </div>

              <div className="space-y-2">
                <Button
                  onClick={() => testForm('suggestion', { ...suggestionData, created_by: user?.id })}
                  disabled={loading}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  <Send className="w-4 h-4 mr-2" />
                  اختبار الإرسال
                </Button>

                <Button
                  onClick={() => testDraftSave('suggestion', suggestionData)}
                  disabled={loading}
                  variant="secondary"
                  className="w-full"
                >
                  <Save className="w-4 h-4 mr-2" />
                  اختبار حفظ المسودة
                </Button>

                <Button
                  onClick={() => testValidation('suggestion', suggestionData)}
                  disabled={loading}
                  variant="secondary"
                  className="w-full"
                >
                  <Target className="w-4 h-4 mr-2" />
                  اختبار التحقق
                </Button>
              </div>

              {errors.suggestion && (
                <ErrorMessage message={errors.suggestion} className="mt-3" />
              )}

              {/* Results for Suggestion */}
              {errors.suggestion && (
                <ErrorMessage message={errors.suggestion} className="mt-3" />
              )}

              {results.suggestion && (
                <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 font-medium">تم الإرسال بنجاح!</p>
                  <p className="text-sm text-green-600">
                    رقم الطلب: {results.suggestion.data?.request_number}
                  </p>
                </div>
              )}

              {/* Draft Results */}
              {draftResults.suggestion && (
                <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-blue-800 font-medium">نتائج اختبار المسودة:</p>
                  <div className="text-sm text-blue-600 mt-1">
                    <div>✓ حفظ المسودة: {draftResults.suggestion.saved ? 'نجح' : 'فشل'}</div>
                    <div>✓ استرجاع المسودة: {draftResults.suggestion.retrieved ? 'نجح' : 'فشل'}</div>
                  </div>
                </div>
              )}
            </Card>
          </div>

          {/* Reset Button */}
          {(Object.keys(results).length > 0 || Object.keys(errors).length > 0) && (
            <div className="mt-6 text-center">
              <Button
                onClick={() => {
                  setResults({})
                  setErrors({})
                  setDraftResults({})
                  setTestingPhase('idle')
                }}
                variant="secondary"
                className="flex items-center gap-2 mx-auto"
              >
                <RefreshCw className="w-4 h-4" />
                مسح النتائج وإعادة التعيين
              </Button>
            </div>
          )}

          {/* Quick Links */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
            <Link
              href="/testing/forms/submission"
              className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow"
            >
              <h4 className="font-medium text-gray-900 mb-2">اختبار الإرسال</h4>
              <p className="text-sm text-gray-600">اختبار عملية إرسال النماذج</p>
            </Link>

            <Link
              href="/testing/forms/unified-advanced"
              className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow"
            >
              <h4 className="font-medium text-gray-900 mb-2">الاختبار المتقدم</h4>
              <p className="text-sm text-gray-600">اختبار النموذج الموحد مع جميع التحسينات</p>
            </Link>

            <Link
              href="/testing/forms/performance"
              className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow"
            >
              <h4 className="font-medium text-gray-900 mb-2">اختبار الأداء</h4>
              <p className="text-sm text-gray-600">قياس سرعة النظام وأوقات الاستجابة</p>
            </Link>

            <Link
              href="/requests/new"
              className="p-4 bg-blue-50 rounded-lg border border-blue-200 hover:shadow-md transition-shadow"
            >
              <h4 className="font-medium text-blue-900 mb-2">إنشاء طلب حقيقي</h4>
              <p className="text-sm text-blue-600">انتقل لإنشاء طلب فعلي</p>
            </Link>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}
