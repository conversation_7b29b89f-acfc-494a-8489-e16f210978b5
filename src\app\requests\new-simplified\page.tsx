'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  FileText, 
  MessageSquare, 
  ArrowRight,
  CheckCircle,
  Clock,
  Users,
  Target,
  Lightbulb
} from 'lucide-react'

type RequestType = 'project' | 'proposal'

export default function NewSimplifiedRequestPage() {
  const [selectedType, setSelectedType] = useState<RequestType | null>(null)
  const router = useRouter()

  const requestTypes = [
    {
      id: 'project' as const,
      title: 'مشروع',
      subtitle: 'مبادرة للتنفيذ المباشر',
      description: 'لديك فكرة واضحة ومحددة تريد تنفيذها كمشروع',
      icon: FileText,
      color: 'blue',
      features: [
        'خطة تنفيذ واضحة',
        'فريق عمل محدد',
        'جدول زمني للتنفيذ',
        'موارد مخصصة'
      ],
      process: [
        'تعبئة معلومات المشروع',
        'تحديد الفريق والموارد',
        'وضع الخطة الزمنية',
        'المراجعة والموافقة',
        'بدء التنفيذ'
      ],
      duration: '2-6 أشهر',
      complexity: 'متوسط إلى معقد'
    },
    {
      id: 'proposal' as const,
      title: 'مقترح',
      subtitle: 'فكرة للدراسة والتقييم',
      description: 'لديك فكرة أو اقتراح تريد دراسته وتقييمه قبل التنفيذ',
      icon: MessageSquare,
      color: 'green',
      features: [
        'وصف المشكلة أو الفرصة',
        'الحلول المقترحة',
        'تقييم الجدوى',
        'التوصيات'
      ],
      process: [
        'وصف المشكلة',
        'اقتراح الحلول',
        'تقييم الخيارات',
        'المراجعة والموافقة',
        'تحويل إلى مشروع (إذا تمت الموافقة)'
      ],
      duration: '2-4 أسابيع',
      complexity: 'بسيط إلى متوسط'
    }
  ]

  const getColorClasses = (color: string) => ({
    card: {
      blue: 'border-blue-200 hover:border-blue-300 bg-blue-50 hover:bg-blue-100',
      green: 'border-green-200 hover:border-green-300 bg-green-50 hover:bg-green-100'
    }[color],
    icon: {
      blue: 'text-blue-600',
      green: 'text-green-600'
    }[color],
    badge: {
      blue: 'bg-blue-100 text-blue-800',
      green: 'bg-green-100 text-green-800'
    }[color],
    button: {
      blue: 'bg-blue-600 hover:bg-blue-700 text-white',
      green: 'bg-green-600 hover:bg-green-700 text-white'
    }[color]
  })

  const handleContinue = () => {
    if (selectedType === 'project') {
      router.push('/requests/new-project')
    } else if (selectedType === 'proposal') {
      router.push('/requests/new-proposal')
    }
  }

  return (
    <ProtectedLayout>
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            طلب تحسين جديد
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            اختر نوع الطلب المناسب لاحتياجاتك
          </p>
          
          {/* سير العمل المبسط */}
          <div className="bg-gray-50 rounded-lg p-6 max-w-4xl mx-auto">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              سير العمل المبسط
            </h2>
            <div className="flex items-center justify-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white font-semibold">
                  1
                </div>
                <span className="text-gray-700">طلب تحسين</span>
              </div>
              <ArrowRight className="w-4 h-4 text-gray-400" />
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                  2
                </div>
                <span className="text-gray-700">اختيار النوع</span>
              </div>
              <ArrowRight className="w-4 h-4 text-gray-400" />
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                  3
                </div>
                <span className="text-gray-700">تعبئة النموذج</span>
              </div>
              <ArrowRight className="w-4 h-4 text-gray-400" />
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold">
                  4
                </div>
                <span className="text-gray-700">المراجعة والموافقة</span>
              </div>
            </div>
          </div>
        </div>

        {/* Request Types Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {requestTypes.map((type) => {
            const Icon = type.icon
            const colors = getColorClasses(type.color)
            const isSelected = selectedType === type.id

            return (
              <Card
                key={type.id}
                className={`p-8 cursor-pointer transition-all duration-300 ${
                  isSelected 
                    ? `${colors.card} ring-2 ring-offset-2 ${type.color === 'blue' ? 'ring-blue-500' : 'ring-green-500'} shadow-lg transform scale-105`
                    : `${colors.card} hover:shadow-lg hover:transform hover:scale-102`
                }`}
                onClick={() => setSelectedType(type.id)}
              >
                <div className="text-center mb-6">
                  <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full bg-white shadow-md mb-4 ${
                    isSelected ? 'ring-2 ring-offset-2 ring-gray-200' : ''
                  }`}>
                    <Icon className={`w-10 h-10 ${colors.icon}`} />
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {type.title}
                  </h3>
                  
                  <p className="text-lg font-medium text-gray-700 mb-3">
                    {type.subtitle}
                  </p>
                  
                  <p className="text-gray-600 mb-6">
                    {type.description}
                  </p>
                </div>

                {/* مؤشرات المشروع */}
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      المدة المتوقعة:
                    </span>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${colors.badge}`}>
                      {type.duration}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <Target className="w-4 h-4" />
                      مستوى التعقيد:
                    </span>
                    <span className="text-sm text-gray-600">{type.complexity}</span>
                  </div>
                </div>

                {/* المميزات */}
                <div className="space-y-3 mb-6">
                  <h4 className="text-sm font-semibold text-gray-800 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    المميزات:
                  </h4>
                  {type.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-gray-700">
                      <div className={`w-2 h-2 rounded-full ${colors.icon?.replace('text-', 'bg-') || 'bg-gray-400'}`} />
                      {feature}
                    </div>
                  ))}
                </div>

                {/* خطوات العملية */}
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-gray-800 flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    خطوات العملية:
                  </h4>
                  {type.process.map((step, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-gray-700">
                      <div className={`w-5 h-5 rounded-full ${colors.icon?.replace('text-', 'bg-') || 'bg-gray-400'} text-white text-xs flex items-center justify-center font-semibold`}>
                        {index + 1}
                      </div>
                      {step}
                    </div>
                  ))}
                </div>

                {isSelected && (
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <div className="flex items-center justify-center gap-2 text-sm font-medium text-gray-700">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      تم الاختيار
                    </div>
                  </div>
                )}
              </Card>
            )
          })}
        </div>

        {/* Continue Button */}
        {selectedType && (
          <div className="text-center">
            <Button
              onClick={handleContinue}
              className={`px-8 py-3 text-lg font-semibold rounded-lg transition-all duration-200 ${
                getColorClasses(requestTypes.find(t => t.id === selectedType)?.color || 'blue').button
              }`}
            >
              متابعة مع {requestTypes.find(t => t.id === selectedType)?.title}
              <ArrowRight className="w-5 h-5 mr-2" />
            </Button>
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="mt-16 bg-blue-50 rounded-lg p-6 max-w-4xl mx-auto">
          <div className="text-center">
            <Lightbulb className="w-8 h-8 text-blue-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-blue-900 mb-3">
              نصائح للاختيار الصحيح
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-800">
              <div className="bg-white rounded-lg p-4">
                <h3 className="font-semibold mb-2">اختر "مشروع" إذا:</h3>
                <ul className="space-y-1 text-right">
                  <li>• لديك خطة واضحة للتنفيذ</li>
                  <li>• تعرف الموارد المطلوبة</li>
                  <li>• لديك فريق عمل جاهز</li>
                  <li>• الهدف محدد وقابل للقياس</li>
                </ul>
              </div>
              <div className="bg-white rounded-lg p-4">
                <h3 className="font-semibold mb-2">اختر "مقترح" إذا:</h3>
                <ul className="space-y-1 text-right">
                  <li>• لديك فكرة تحتاج دراسة</li>
                  <li>• تريد تقييم عدة خيارات</li>
                  <li>• تحتاج موافقة قبل التنفيذ</li>
                  <li>• الفكرة في مرحلة التطوير</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}
