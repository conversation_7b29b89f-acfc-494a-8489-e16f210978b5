'use client'

import React, { useState } from 'react'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { UnifiedProjectForm, FormType } from '@/components/forms/unified/UnifiedProjectForm'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { WorkflowExplanation } from '@/components/shared/WorkflowExplanation'
import { useAuth } from '@/lib/core/auth'
import { useAutoDraft } from '@/hooks/useAutoDraft'
import { 
  FileText, 
  TrendingUp, 
  Lightbulb, 
  Zap,
  MessageSquare,
  ArrowRight,
  ChevronLeft
} from 'lucide-react'

type RequestCategory = 'general' | 'improvement'
type RequestType = 'enhanced_improvement' | 'quick_win' | 'suggestion'

export default function NewRequestPage() {
  const [selectedCategory, setSelectedCategory] = useState<RequestCategory | null>(null)
  const [selectedType, setSelectedType] = useState<RequestType | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  const { user } = useAuth()

  // دالة حفظ المسودة
  const handleSaveDraft = async (data: any): Promise<void> => {
    // التحقق من وجود المستخدم
    if (!user?.id) {
      throw new Error('يجب تسجيل الدخول أولاً لحفظ المسودة')
    }

    if (!selectedType) {
      throw new Error('يجب اختيار نوع النموذج أولاً')
    }

    // تحضير بيانات المسودة
    const draftData = {
      form_type: selectedType,
      form_data: data,
      user_id: user.id,
    }

    // إرسال المسودة إلى API
    const response = await fetch('/api/requests/drafts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(draftData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      const errorMessage = errorData.error || 'حدث خطأ في حفظ المسودة'

      // معالجة خاصة لأخطاء المصادقة
      if (response.status === 401) {
        window.location.href = '/auth/login'
        throw new Error('انتهت صلاحية جلسة المستخدم')
      }

      throw new Error(errorMessage)
    }

    const result = await response.json()
    // لا نعرض تنبيه للحفظ التلقائي
    return result
  }

  // ملاحظة: نظام الحفظ التلقائي سيتم تفعيله من داخل UnifiedProjectForm

  // الفئات الرئيسية
  const requestCategories = [
    {
      id: 'general' as const,
      title: 'طلب مشروع عام',
      description: 'طلبات المشاريع العامة والتطويرية',
      icon: FileText,
      color: 'blue',
      comingSoon: true
    },
    {
      id: 'improvement' as const,
      title: 'طلب مشروع تحسين',
      description: 'مشاريع التحسين والتطوير المستمر',
      icon: TrendingUp,
      color: 'green',
      comingSoon: false
    }
  ]

  // أنواع مشاريع التحسين
  const improvementTypes = [
    {
      id: 'quick_win' as const,
      title: 'مشروع كويك وين',
      subtitle: '(مشروع بسيط)',
      description: 'مخصص لمالك المشروع - حلول سريعة وفعالة للمشاكل البسيطة',
      icon: Zap,
      color: 'yellow',
      features: ['تنفيذ سريع', 'تكلفة منخفضة', 'نتائج فورية', 'حد أقصى 4 أسابيع'],
      duration: '1-4 أسابيع',
      complexity: 'بسيط'
    },
    {
      id: 'enhanced_improvement' as const,
      title: 'مشروع تحسين',
      subtitle: 'مخصص لمالك المشروع',
      description: 'منهجية FOCUS-PDCA لمشاريع التحسين',
      icon: TrendingUp,
      color: 'purple',
      features: ['منهجية FOCUS-PDCA', '9 مراحل متقدمة', 'تحليل الأسباب الجذرية', 'نظام مؤشرات متطور'],
      duration: '2-6 أشهر',
      complexity: 'متوسط إلى معقد'
    },
    {
      id: 'suggestion' as const,
      title: 'مقترح مشروع تحسين',
      subtitle: 'مخصص لفريق الجودة أو مقترح تحسين',
      description: 'اقتراح حلول متعددة للمشاكل والفرص التحسينية',
      icon: MessageSquare,
      color: 'indigo',
      features: ['حلول متعددة', 'تحليل الجدوى', 'مبررات واضحة', 'تقييم التأثير'],
      duration: '2-8 أسابيع',
      complexity: 'متوسط'
    }
  ]

  const handleSubmit = async (data: any) => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      // التحقق من وجود المستخدم
      if (!user?.id) {
        setError('يجب تسجيل الدخول أولاً لإرسال الطلب')
        return
      }

      // إنشاء رقم الطلب حسب النوع
      const requestNumber = generateRequestNumber(selectedType!)
      
      // تحضير البيانات للحفظ
      const requestData = {
        request_number: requestNumber,
        form_type: selectedType,
        form_data: data,
        status: selectedType === 'suggestion' ? 'submitted' : 'submitted',
        created_by: user.id,
        created_at: new Date().toISOString(),
      }

      // إرسال البيانات إلى API
      const response = await fetch('/api/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        const errorMessage = errorData.error || 'حدث خطأ في إرسال الطلب'
        
        // معالجة خاصة لأخطاء المصادقة
        if (response.status === 401) {
          alert('انتهت صلاحية جلسة المستخدم. يرجى تسجيل الدخول مرة أخرى.')
          window.location.href = '/auth/login'
          return
        }
        
        throw new Error(errorMessage)
      }

      const result = await response.json()

      // عرض رسالة نجاح مع رقم الطلب
      const successMessage = selectedType === 'suggestion' 
        ? `تم إرسال المقترح بنجاح! رقم طلب المقترح: ${requestNumber}. سيتم مراجعة المقترح من قبل أصحاب المصلحة وسيتم إشعارك بالنتيجة قريباً. شكراً لمساهمتك في التحسين المستمر!`
        : `تم إرسال الطلب بنجاح! رقم طلب المشروع: ${requestNumber}. سيتم مراجعة الطلب والرد عليك قريباً. شكراً لك!`
      
      setSuccess(successMessage)
      
      // إعادة توجيه لصفحة الطلبات بعد 3 ثوانٍ
      setTimeout(() => window.location.href = '/requests', 3000)
      
    } catch (error) {
      console.error('Error submitting request:', error)
      setError(error instanceof Error ? error.message : 'حدث خطأ أثناء إرسال الطلب')
    } finally {
      setIsLoading(false)
    }
  }

  // دالة إنشاء رقم الطلب
  const generateRequestNumber = (type: RequestType): string => {
    const year = new Date().getFullYear()
    const month = String(new Date().getMonth() + 1).padStart(2, '0')
    const day = String(new Date().getDate()).padStart(2, '0')
    const time = String(Date.now()).slice(-4) // آخر 4 أرقام من timestamp
    
    const prefix = type === 'suggestion' ? 'SUG' : 'REQ'
    return `${prefix}-${year}${month}${day}-${time}`
  }



  // دالة الحفظ اليدوي مع عرض رسالة
  const handleManualSaveDraft = async (data: any) => {
    try {
      await handleSaveDraft(data)
      alert('تم حفظ المسودة بنجاح!')
    } catch (error) {
      console.error('Error saving draft:', error)
      alert(error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ المسودة')
    }
  }

  const renderForm = () => {
    if (!selectedType) return null
    
    // التحقق من تسجيل الدخول قبل عرض النموذج
    if (!user) {
      return (
        <Card className="p-6 text-center">
          <p className="text-gray-600 mb-4">يجب تسجيل الدخول لإنشاء طلب جديد</p>
          <Button 
            onClick={() => window.location.href = '/auth/login'}
            variant="primary"
          >
            تسجيل الدخول
          </Button>
        </Card>
      )
    }
    
    return (
      <UnifiedProjectForm
        formType={selectedType}
        onSubmit={handleSubmit}
        onSaveDraft={handleManualSaveDraft}
        isLoading={isLoading}
      />
    )
  }

  const getColorClasses = (color: string) => ({
    card: {
      blue: 'border-blue-200 hover:border-blue-300 bg-blue-50',
      green: 'border-green-200 hover:border-green-300 bg-green-50',
      purple: 'border-purple-200 hover:border-purple-300 bg-purple-50',
      yellow: 'border-yellow-200 hover:border-yellow-300 bg-yellow-50',
      indigo: 'border-indigo-200 hover:border-indigo-300 bg-indigo-50'
    }[color],
    icon: {
      blue: 'text-blue-600',
      green: 'text-green-600',
      purple: 'text-purple-600',
      yellow: 'text-yellow-600',
      indigo: 'text-indigo-600'
    }[color],
    badge: {
      blue: 'bg-blue-100 text-blue-800',
      green: 'bg-green-100 text-green-800',
      purple: 'bg-purple-100 text-purple-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      indigo: 'bg-indigo-100 text-indigo-800'
    }[color]
  })

  const renderContent = () => {
    // إذا تم اختيار نوع معين، عرض النموذج
    if (selectedType) {
      const selectedTypeData = improvementTypes.find(t => t.id === selectedType)
      return (
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button
              onClick={() => setSelectedType(null)}
              variant="ghost"
              className="flex items-center gap-2"
            >
              <ChevronLeft className="w-4 h-4" />
              العودة للاختيار
            </Button>
            <div>
              <h1 className="heading-main text-gray-900">
                {selectedTypeData?.title}
              </h1>
              <p className="text-caption text-gray-600">{selectedTypeData?.subtitle}</p>
            </div>
          </div>
          
          {/* رسائل الخطأ والنجاح */}
          {error && (
            <ErrorMessage 
              message={error} 
              onClose={() => setError(null)}
            />
          )}
          
          {success && (
            <ErrorMessage 
              title="تم بنجاح"
              message={success} 
              type="info"
              onClose={() => setSuccess(null)}
            />
          )}
          
          {renderForm()}
        </div>
      )
    }

    // إذا تم اختيار فئة التحسين، عرض أنواع مشاريع التحسين
    if (selectedCategory === 'improvement') {
      return (
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <Button
              onClick={() => setSelectedCategory(null)}
              variant="ghost"
              className="mb-4 flex items-center gap-2 mx-auto"
            >
              <ChevronLeft className="w-4 h-4" />
              العودة للفئات الرئيسية
            </Button>
            <h1 className="heading-main text-gray-900 mb-4">مشاريع التحسين</h1>
            <p className="heading-sub text-gray-600">
              اختر نوع مشروع التحسين المناسب لاحتياجاتك
            </p>
          </div>

          {/* Improvement Types Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {improvementTypes.map((type) => {
              const Icon = type.icon
              const colors = getColorClasses(type.color)

              return (
                <Card
                  key={type.id}
                  className={`p-6 cursor-pointer transition-all duration-200 ${colors.card} hover:shadow-lg h-full`}
                  onClick={() => setSelectedType(type.id)}
                >
                  <div className="text-center mb-6">
                    <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-white mb-4`}>
                      <Icon className={`w-8 h-8 ${colors.icon}`} />
                    </div>
                                    <h3 className="heading-sub text-gray-900 mb-1">{type.title}</h3>
                <p className="text-caption font-medium text-gray-700 mb-2">{type.subtitle}</p>
                <p className="text-gray-600 text-caption mb-4">{type.description}</p>
                  </div>

                  {/* مؤشرات المشروع */}
                  <div className="space-y-3 mb-6">
                    <div className="flex justify-between items-center">
                      <span className="text-caption font-medium text-gray-700">المدة المتوقعة:</span>
                      <span className={`px-2 py-1 rounded-full text-label font-medium ${colors.badge}`}>
                        {type.duration}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-caption font-medium text-gray-700">مستوى التعقيد:</span>
                      <span className="text-caption text-gray-600">{type.complexity}</span>
                    </div>
                  </div>

                  {/* المميزات */}
                  <div className="space-y-2 mb-6">
                    <h4 className="text-caption font-semibold text-gray-800">المميزات:</h4>
                    {type.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2 text-caption text-gray-700">
                        <div className={`w-2 h-2 rounded-full ${colors.icon?.replace('text-', 'bg-') || 'bg-gray-400'}`} />
                        {feature}
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center justify-center gap-2 text-caption font-medium text-gray-700 mt-auto">
                    اختيار هذا النوع
                    <ArrowRight className="w-4 h-4" />
                  </div>
                </Card>
              )
            })}
          </div>

          {/* دليل الاختيار */}
          <div className="mt-12 bg-gray-50 rounded-lg p-6">
            <h2 className="heading-sub text-gray-900 mb-4 text-center">دليل اختيار نوع مشروع التحسين</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-caption text-gray-700">
              <div className="bg-white rounded-lg p-4">
                <h3 className="font-semibold mb-3 text-yellow-800">اختر كويك وين إذا:</h3>
                <ul className="space-y-2">
                  <li>• المشكلة بسيطة وواضحة</li>
                  <li>• تريد حل سريع (أقل من 4 أسابيع)</li>
                  <li>• التكلفة والموارد محدودة</li>
                  <li>• أنت مالك المشروع أو المسؤول المباشر</li>
                </ul>
              </div>
              <div className="bg-white rounded-lg p-4">
                <h3 className="font-semibold mb-3 text-purple-800">اختر مشروع التحسين الشامل إذا:</h3>
                <ul className="space-y-2">
                  <li>• تريد تحليل عميق للمشكلة</li>
                  <li>• تحتاج منهجية FOCUS-PDCA المتقدمة</li>
                  <li>• المشكلة معقدة وتحتاج دراسة شاملة</li>
                  <li>• لديك الوقت والموارد الكافية</li>
                </ul>
              </div>
              <div className="bg-white rounded-lg p-4">
                <h3 className="font-semibold mb-3 text-indigo-800">اختر المقترح التحسيني إذا:</h3>
                <ul className="space-y-2">
                  <li>• لديك أفكار متعددة لحل مشكلة</li>
                  <li>• تريد مقارنة الحلول المختلفة</li>
                  <li>• تحتاج تقييم الجدوى والتأثير</li>
                  <li>• تعمل في فريق الجودة أو التحسين</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )
    }

    // الصفحة الرئيسية - اختيار الفئة
    return (
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="heading-main text-gray-900 mb-4">طلب مشروع</h1>
          <p className="heading-sub text-gray-600">
            اختر فئة المشروع المناسبة لبدء طلبك
          </p>
        </div>

        {/* شرح سير العمل */}
        <div className="mb-8">
          <WorkflowExplanation />
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {requestCategories.map((category) => {
            const Icon = category.icon
            const colors = getColorClasses(category.color)
            const isDisabled = category.comingSoon

            return (
              <Card
                key={category.id}
                className={`p-8 transition-all duration-200 relative ${
                  isDisabled 
                    ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-75' 
                    : `${colors.card} hover:shadow-xl cursor-pointer transform hover:-translate-y-1`
                }`}
                onClick={() => !isDisabled && setSelectedCategory(category.id)}
              >
                {isDisabled && (
                  <div className="absolute top-4 right-4">
                    <span className="bg-gray-500 text-white px-3 py-1 rounded-full text-label font-medium">
                      قريباً
                    </span>
                  </div>
                )}
                
                <div className="text-center">
                  <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full bg-white mb-6 ${
                    isDisabled ? 'shadow-sm' : 'shadow-md'
                  }`}>
                    <Icon className={`w-10 h-10 ${isDisabled ? 'text-gray-400' : colors.icon}`} />
                  </div>
                  
                  <h3 className={`heading-main mb-3 ${
                    isDisabled ? 'text-gray-500' : 'text-gray-900'
                  }`}>
                    {category.title}
                  </h3>
                  
                  <p className={`heading-sub mb-6 ${
                    isDisabled ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    {category.description}
                  </p>

                  {!isDisabled && (
                    <div className="flex items-center justify-center gap-2 text-body font-medium text-gray-700">
                      اختيار هذه الفئة
                      <ArrowRight className="w-5 h-5" />
                    </div>
                  )}
                </div>
              </Card>
            )
          })}
        </div>

        {/* معلومات إضافية */}
        <div className="mt-16 text-center">
          <div className="bg-blue-50 rounded-lg p-6 max-w-2xl mx-auto">
            <h2 className="heading-section text-blue-900 mb-3">
              💡 نصيحة
            </h2>
            <p className="text-blue-800">
              إذا كنت تريد تحسين عملية موجودة أو حل مشكلة معينة، اختر &quot;طلب مشروع تحسين&quot;.
              أما إذا كنت تريد إنشاء مشروع جديد من الصفر، فاختر &quot;طلب مشروع عام&quot; (قريباً).
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <ProtectedLayout>
      {renderContent()}
    </ProtectedLayout>
  )
} 