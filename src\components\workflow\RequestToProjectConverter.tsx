'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input, Textarea, Select } from '@/components/ui/Input'
import { 
  ArrowRight, 
  CheckCircle, 
  User, 
  Calendar,
  Target,
  AlertTriangle,
  FileText,
  Settings
} from 'lucide-react'

interface ProjectRequest {
  id: string
  title: string
  description: string
  main_type: string
  sub_type: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  requester: {
    name: string
    email: string
  }
  form_data: any
  created_at: string
}

interface ProjectData {
  title: string
  description: string
  project_manager_id: string
  methodology: 'pdca' | 'agile' | 'waterfall'
  start_date: string
  end_date: string
  budget: number
  priority: 'low' | 'medium' | 'high' | 'urgent'
}

interface RequestToProjectConverterProps {
  request: ProjectRequest
  onConvert: (projectData: ProjectData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function RequestToProjectConverter({ 
  request, 
  onConvert, 
  onCancel, 
  isLoading = false 
}: RequestToProjectConverterProps) {
  const [projectData, setProjectData] = useState<ProjectData>({
    title: request.title,
    description: request.description,
    project_manager_id: '',
    methodology: 'pdca',
    start_date: '',
    end_date: '',
    budget: 0,
    priority: request.priority
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!projectData.title.trim()) {
      newErrors.title = 'عنوان المشروع مطلوب'
    }

    if (!projectData.description.trim()) {
      newErrors.description = 'وصف المشروع مطلوب'
    }

    if (!projectData.project_manager_id) {
      newErrors.project_manager_id = 'مدير المشروع مطلوب'
    }

    if (!projectData.start_date) {
      newErrors.start_date = 'تاريخ البداية مطلوب'
    }

    if (!projectData.end_date) {
      newErrors.end_date = 'تاريخ النهاية مطلوب'
    } else if (projectData.start_date && new Date(projectData.end_date) <= new Date(projectData.start_date)) {
      newErrors.end_date = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية'
    }

    if (projectData.budget < 0) {
      newErrors.budget = 'الميزانية يجب أن تكون رقم موجب'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (validateForm()) {
      await onConvert(projectData)
    }
  }

  const updateField = (field: keyof ProjectData, value: any) => {
    setProjectData(prev => ({
      ...prev,
      [field]: value
    }))

    // إزالة الخطأ عند التحديث
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const methodologyOptions = [
    { value: 'pdca', label: 'PDCA (Plan-Do-Check-Act)' },
    { value: 'agile', label: 'Agile (رشيق)' },
    { value: 'waterfall', label: 'Waterfall (شلالي)' }
  ]

  const priorityOptions = [
    { value: 'low', label: 'منخفضة' },
    { value: 'medium', label: 'متوسطة' },
    { value: 'high', label: 'عالية' },
    { value: 'urgent', label: 'عاجلة' }
  ]

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-green-50 border-blue-200">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <FileText className="h-8 w-8 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                تحويل المقترح إلى مشروع
              </h2>
              <p className="text-gray-600">
                إكمال البيانات المطلوبة لإنشاء مشروع جديد
              </p>
            </div>
          </div>
          <ArrowRight className="h-6 w-6 text-gray-400" />
          <div className="flex items-center gap-3">
            <Settings className="h-8 w-8 text-green-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                مشروع جديد
              </h3>
              <p className="text-gray-600 text-sm">
                جاهز للتنفيذ
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* معلومات المقترح الأصلي */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <FileText className="h-5 w-5 text-blue-600" />
          معلومات المقترح الأصلي
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">العنوان:</span>
            <p className="text-gray-600">{request.title}</p>
          </div>
          <div>
            <span className="font-medium text-gray-700">النوع:</span>
            <p className="text-gray-600">{request.sub_type}</p>
          </div>
          <div>
            <span className="font-medium text-gray-700">مقدم الطلب:</span>
            <p className="text-gray-600">{request.requester.name}</p>
          </div>
          <div>
            <span className="font-medium text-gray-700">تاريخ الإنشاء:</span>
            <p className="text-gray-600">
              {new Date(request.created_at).toLocaleDateString('ar-SA')}
            </p>
          </div>
        </div>
      </Card>

      {/* نموذج بيانات المشروع */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Settings className="h-5 w-5 text-green-600" />
          بيانات المشروع الجديد
        </h3>

        <div className="space-y-6">
          {/* العنوان والوصف */}
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                عنوان المشروع *
              </label>
              <Input
                value={projectData.title}
                onChange={(e) => updateField('title', e.target.value)}
                error={errors.title}
                placeholder="عنوان المشروع"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                وصف المشروع *
              </label>
              <Textarea
                value={projectData.description}
                onChange={(e) => updateField('description', e.target.value)}
                error={errors.description}
                placeholder="وصف تفصيلي للمشروع وأهدافه"
                rows={4}
              />
            </div>
          </div>

          {/* المنهجية والأولوية */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                منهجية إدارة المشروع *
              </label>
              <Select
                value={projectData.methodology}
                onChange={(e) => updateField('methodology', e.target.value)}
                options={methodologyOptions}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الأولوية *
              </label>
              <Select
                value={projectData.priority}
                onChange={(e) => updateField('priority', e.target.value)}
                options={priorityOptions}
              />
            </div>
          </div>

          {/* التواريخ والميزانية */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تاريخ البداية *
              </label>
              <Input
                type="date"
                value={projectData.start_date}
                onChange={(e) => updateField('start_date', e.target.value)}
                error={errors.start_date}
                min={new Date().toISOString().split('T')[0]}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تاريخ النهاية المتوقع *
              </label>
              <Input
                type="date"
                value={projectData.end_date}
                onChange={(e) => updateField('end_date', e.target.value)}
                error={errors.end_date}
                min={projectData.start_date || new Date().toISOString().split('T')[0]}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الميزانية المقدرة (ريال)
              </label>
              <Input
                type="number"
                value={projectData.budget}
                onChange={(e) => updateField('budget', parseFloat(e.target.value) || 0)}
                error={errors.budget}
                placeholder="0"
                min="0"
                step="1000"
              />
            </div>
          </div>

          {/* مدير المشروع */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              مدير المشروع *
            </label>
            <Input
              value={projectData.project_manager_id}
              onChange={(e) => updateField('project_manager_id', e.target.value)}
              error={errors.project_manager_id}
              placeholder="اختر مدير المشروع"
            />
            <p className="text-xs text-gray-500 mt-1">
              سيتم تطوير قائمة اختيار المديرين لاحقاً
            </p>
          </div>
        </div>
      </Card>

      {/* أزرار التحكم */}
      <div className="flex justify-between">
        <Button
          variant="secondary"
          onClick={onCancel}
          disabled={isLoading}
        >
          إلغاء
        </Button>

        <Button
          onClick={handleSubmit}
          disabled={isLoading}
          className="bg-green-600 hover:bg-green-700"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              جاري التحويل...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              تحويل إلى مشروع
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
