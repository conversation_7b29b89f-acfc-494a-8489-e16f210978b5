'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth, usePermissions } from '@/lib/core/auth'
import {
  Home,
  FolderPlus,
  CheckSquare,
  BarChart3,
  Users,
  Building,
  Settings,
  LogOut,
  ChevronLeft,
  ChevronRight,
  User,
  Bell,
  FileText,
  TrendingUp,
  Calendar,
  Archive,
  FolderOpen,
  Target,
  Activity,
  Shield,
  Database,
  Zap,
  HelpCircle,
  TestTube,
  GitBranch
} from 'lucide-react'

interface SidebarProps {
  isCollapsed: boolean
  onToggle: () => void
}

export function Sidebar({ isCollapsed, onToggle }: SidebarProps) {
  const { user, userRole, signOut } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()
  const pathname = usePathname()
  const [requestsCount, setRequestsCount] = useState<string | null>(null)
  const [approvalsCount, setApprovalsCount] = useState<string | null>(null)
  
  // تحميل عدد الطلبات والموافقات
  useEffect(() => {
    const fetchCounts = async () => {
      try {
        // تحميل عدد الطلبات
        const requestsResponse = await fetch('/api/requests')
        if (requestsResponse.ok) {
          const requestsData = await requestsResponse.json()
          const count = requestsData.data?.length || 0
          setRequestsCount(count > 0 ? count.toString() : null)
        }
        
        // تحميل عدد الموافقات المعلقة (إذا كان المستخدم لديه صلاحية)
        if (permissions?.canApproveRequests()) {
          const approvalsResponse = await fetch('/api/approvals?status=pending')
          if (approvalsResponse.ok) {
            const approvalsData = await approvalsResponse.json()
            const count = approvalsData.data?.length || 0
            setApprovalsCount(count > 0 ? count.toString() : null)
          }
        }
      } catch (error) {
        console.error('Error fetching counts:', error)
      }
    }
    
    fetchCounts()
  }, [])

  const handleSignOut = async () => {
    await signOut()
    router.push('/auth/login')
  }

  // تحقق من تحميل البيانات
  if (!user || !userRole) {
    return (
      <div className={`
        fixed top-0 right-0 h-full bg-white border-l border-gray-200 shadow-lg z-40 transition-all duration-300 ease-in-out
        ${isCollapsed ? 'w-16' : 'w-64'}
      `}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-sm text-gray-500 mt-2">جاري التحميل...</p>
          </div>
        </div>
      </div>
    )
  }

  const navigationItems = [
    {
      id: 'dashboard',
      name: 'لوحة التحكم',
      href: '/dashboard',
      icon: Home,
      show: true,
      badge: null
    },
    {
      id: 'admin-dashboard',
      name: 'لوحة التحكم الإدارية',
      href: '/admin/dashboard',
      icon: Shield,
      show: permissions?.isAdmin() || permissions?.isPMOManager() || userRole?.name === 'admin',
      badge: null
    },
    {
      id: 'requests',
      name: 'طلبات المشاريع',
      href: '/requests',
      icon: FolderPlus,
      show: true, // مرئي لجميع المستخدمين
      badge: requestsCount // عدد الطلبات الحقيقية
    },
    {
      id: 'approvals',
      name: 'الموافقات',
      href: '/approvals',
      icon: CheckSquare,
      show: permissions?.canApproveRequests() || userRole?.name === 'admin' || userRole?.name === 'pmo_manager' || userRole?.name === 'planning_manager',
      badge: approvalsCount // عدد الموافقات المعلقة
    },
    {
      id: 'projects',
      name: 'إدارة المشاريع',
      href: '/projects',
      icon: BarChart3,
      show: true, // مرئي لجميع المستخدمين
      badge: null
    },
    {
      id: 'convert-to-project',
      name: 'تحويل إلى مشاريع',
      href: '/convert-to-project',
      icon: Zap,
      show: permissions?.canApproveRequests() || userRole?.name === 'admin' || userRole?.name === 'pmo_manager' || userRole?.name === 'planning_manager',
      badge: null
    },
    {
      id: 'kpis',
      name: 'مؤشرات الأداء',
      href: '/kpis',
      icon: Target,
      show: true, // مرئي لجميع المستخدمين
      badge: null
    },
    {
      id: 'kpi-reports',
      name: 'تقارير المؤشرات',
      href: '/kpis/reports',
      icon: Activity,
      show: true, // مرئي لجميع المستخدمين
      badge: null
    },
    {
      id: 'reports',
      name: 'التقارير',
      href: '/reports',
      icon: FileText,
      show: true, // مرئي لجميع المستخدمين
      badge: null
    },
    {
      id: 'analytics',
      name: 'الإحصائيات',
      href: '/analytics',
      icon: TrendingUp,
      show: true, // مرئي لجميع المستخدمين
      badge: null
    },
    {
      id: 'users',
      name: 'المستخدمين',
      href: '/users',
      icon: Users,
      show: permissions?.isAdmin() || permissions?.isPMOManager() || userRole?.name === 'admin',
      badge: null
    },
    {
      id: 'departments',
      name: 'الأقسام',
      href: '/departments',
      icon: Building,
      show: permissions?.isAdmin() || permissions?.isPMOManager() || userRole?.name === 'admin',
      badge: null
    },
    {
      id: 'permissions',
      name: 'إدارة الصلاحيات',
      href: '/permissions',
      icon: Shield,
      show: permissions?.isAdmin() || permissions?.isPMOManager() || userRole?.name === 'admin',
      badge: null
    },
    {
      id: 'database',
      name: 'قاعدة البيانات',
      href: '/database',
      icon: Database,
      show: permissions?.isAdmin() || userRole?.name === 'admin',
      badge: null
    },
    {
      id: 'system-test',
      name: 'اختبار النظام',
      href: '/system-test',
      icon: Activity,
      show: permissions?.isAdmin() || permissions?.isPMOManager() || userRole?.name === 'admin',
      badge: null
    },
    {
      id: 'security',
      name: 'الأمان',
      href: '/security',
      icon: Shield,
      show: permissions?.isAdmin() || userRole?.name === 'admin',
      badge: null
    },
    {
      id: 'files',
      name: 'إدارة الملفات',
      href: '/files',
      icon: FolderOpen,
      show: true, // مرئي لجميع المستخدمين
      badge: null
    },
    {
      id: 'my-requests',
      name: 'طلباتي',
      href: '/my-requests',
      icon: User,
      show: true, // مرئي لجميع المستخدمين
      badge: null
    },
    {
      id: 'request-new',
      name: 'طلب جديد',
      href: '/requests/new',
      icon: FolderPlus,
      show: true, // مرئي لجميع المستخدمين
      badge: null
    },
    {
      id: 'workflow',
      name: 'دليل سير العمل',
      href: '/workflow',
      icon: GitBranch,
      show: true, // مرئي لجميع المستخدمين
      badge: null
    },
    {
      id: 'help',
      name: 'المساعدة',
      href: '/help',
      icon: HelpCircle,
      show: true, // مرئي لجميع المستخدمين
      badge: null
    },

    {
      id: 'testing',
      name: 'مركز الاختبارات',
      href: '/admin/testing',
      icon: TestTube,
      show: permissions?.isAdmin() || userRole?.name === 'admin',
      badge: null
    },
    {
      id: 'archive',
      name: 'الأرشيف',
      href: '/archive',
      icon: Archive,
      show: permissions?.isAdmin() || permissions?.isPMOManager() || userRole?.name === 'admin',
      badge: null
    }
  ]

  const visibleItems = navigationItems.filter(item => item.show)
  
  // تسجيل للتشخيص
  console.log('User Role:', userRole?.name)
  console.log('Total Items:', navigationItems.length)
  console.log('Visible Items:', visibleItems.length)
  console.log('Visible Items List:', visibleItems.map(item => item.name))

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }

  return (
    <div className={`
      fixed top-0 right-0 h-full bg-white border-l border-gray-200 shadow-lg z-40 transition-all duration-300 ease-in-out
      ${isCollapsed ? 'w-16' : 'w-64'}
    `}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!isCollapsed && (
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center shadow-sm">
              <BarChart3 className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="heading-sub text-gray-900">PMO</h1>
              <p className="text-xs text-gray-500">إدارة المشاريع</p>
            </div>
          </div>
        )}
        
        <div className="flex items-center gap-2">
          {!isCollapsed && (
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
              {visibleItems.length} نظام
            </span>
          )}
          <button
            onClick={onToggle}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            {isCollapsed ? (
              <ChevronLeft className="w-4 h-4 text-gray-600" />
            ) : (
              <ChevronRight className="w-4 h-4 text-gray-600" />
            )}
          </button>
        </div>
      </div>

      {/* User Profile */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center flex-shrink-0">
            <User className="w-5 h-5 text-blue-600" />
          </div>
          {!isCollapsed && (
            <div className="min-w-0 flex-1">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user?.user_metadata?.name || user?.email}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {userRole?.display_name || 'مستخدم'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        {visibleItems.map((item) => {
          const Icon = item.icon
          const active = isActive(item.href)
          
          return (
            <button
              key={item.id}
              onClick={() => router.push(item.href)}
              className={`
                w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 group relative
                ${active 
                  ? 'bg-blue-50 text-blue-700 border border-blue-200 shadow-sm' 
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }
                ${isCollapsed ? 'justify-center' : 'justify-start'}
              `}
            >
              <Icon className={`w-5 h-5 flex-shrink-0 ${active ? 'text-blue-600' : ''}`} />
              
              {!isCollapsed && (
                <>
                  <span className="font-medium truncate">{item.name}</span>
                  {item.badge && (
                    <span className="mr-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full min-w-[20px] text-center">
                      {item.badge}
                    </span>
                  )}
                </>
              )}
              
              {/* Tooltip for collapsed state */}
              {isCollapsed && (
                <div className="absolute right-full mr-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50">
                  {item.name}
                  {item.badge && (
                    <span className="mr-2 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </div>
              )}
              
              {/* Active indicator */}
              {active && (
                <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-blue-600 rounded-r-full"></div>
              )}
            </button>
          )
        })}
      </nav>

      {/* Bottom Actions */}
      <div className="border-t border-gray-200 p-4 space-y-2">
        <button
          onClick={() => router.push('/settings')}
          className={`
            w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 text-gray-600 hover:bg-gray-50 hover:text-gray-900
            ${isCollapsed ? 'justify-center' : 'justify-start'}
          `}
        >
          <Settings className="w-5 h-5" />
          {!isCollapsed && <span className="font-medium">الإعدادات</span>}
        </button>
        
        <button
          onClick={handleSignOut}
          className={`
            w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 text-red-600 hover:bg-red-50
            ${isCollapsed ? 'justify-center' : 'justify-start'}
          `}
        >
          <LogOut className="w-5 h-5" />
          {!isCollapsed && <span className="font-medium">تسجيل الخروج</span>}
        </button>
      </div>
    </div>
  )
} 