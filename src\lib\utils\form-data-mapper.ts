/**
 * محول البيانات بين النماذج القديمة والجديدة
 * Data Mapper for compatibility between old and new forms
 */

import { UnifiedFormData } from '@/components/forms/unified/UnifiedProjectForm'

// أنواع البيانات للنماذج المبسطة
export interface SimplifiedProjectData {
  title: string
  description: string
  department: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  startDate: string
  endDate: string
  problemDescription: string
  currentSituation: string
  targetGoal: string
  successCriteria: string
  proposedSolution: string
  mainTasks: Array<{
    id: string
    title: string
    description: string
    duration: string
  }>
  expectedBenefits: string
  projectManager: {
    name: string
    email: string
    phone: string
  }
  teamMembers: Array<{
    id: string
    name: string
    role: string
    department: string
  }>
  requiredResources: string
  estimatedBudget: number
}

export interface SimplifiedProposalData {
  title: string
  description: string
  department: string
  submitterName: string
  submitterEmail: string
  submitterPhone: string
  problemDescription: string
  currentImpact: string
  affectedAreas: string[]
  urgencyLevel: 'low' | 'medium' | 'high' | 'urgent'
  proposedSolutions: Array<{
    id: string
    title: string
    description: string
    pros: string
    cons: string
    estimatedCost: number
    implementationTime: string
  }>
  recommendedSolution: string
  expectedBenefits: string
  nextSteps: string
}

export type SimplifiedFormData = SimplifiedProjectData | SimplifiedProposalData

// بيانات إضافية للتوافق
export interface CompatibilityData {
  originalFormType: string
  formVersion: string
  legacyData?: {
    indicators?: any[]
    rootCauseAnalysis?: any
    riskManagement?: any
    processDetails?: any
    attachments?: File[]
  }
}

export interface CombinedFormData extends SimplifiedFormData {
  compatibility: CompatibilityData
}

/**
 * فئة محول البيانات
 */
export class FormDataMapper {
  
  /**
   * تحويل من النظام القديم إلى المبسط
   */
  static mapOldToSimplified(
    oldData: UnifiedFormData, 
    targetType: 'project' | 'proposal'
  ): SimplifiedFormData {
    
    if (targetType === 'project') {
      return this.mapToSimplifiedProject(oldData)
    } else {
      return this.mapToSimplifiedProposal(oldData)
    }
  }

  /**
   * تحويل إلى مشروع مبسط
   */
  private static mapToSimplifiedProject(oldData: any): SimplifiedProjectData {
    return {
      // معلومات أساسية
      title: oldData.projectName || oldData.projectTitle || '',
      description: oldData.projectDescription || oldData.problemDescription || '',
      department: oldData.responsibleDepartment || oldData.section || '',
      priority: oldData.priority || 'medium',
      startDate: oldData.startDate || oldData.projectExecutor?.startDate || '',
      endDate: oldData.endDate || '',

      // المشكلة والهدف
      problemDescription: oldData.problemDescription || '',
      currentSituation: oldData.processDescription || oldData.currentSituation || '',
      targetGoal: oldData.targetValue ? `تحقيق ${oldData.targetValue} ${oldData.unit || ''}` : '',
      successCriteria: oldData.indicatorName ? `قياس ${oldData.indicatorName}` : '',

      // الحل والمهام
      proposedSolution: oldData.selectedSolution?.description || oldData.solution?.description || '',
      mainTasks: this.mapTasks(oldData.projectTasks || oldData.solution?.tasks || []),
      expectedBenefits: oldData.selectedSolution?.expectedBenefits || '',

      // الفريق والموارد
      projectManager: {
        name: oldData.teamLeader?.name || oldData.projectExecutor?.name || '',
        email: oldData.teamLeader?.email || oldData.projectExecutor?.email || '',
        phone: oldData.teamLeader?.phone || oldData.projectExecutor?.phone || ''
      },
      teamMembers: this.mapTeamMembers(oldData.teamMembers || []),
      requiredResources: this.mapResources(oldData.requiredResources || []),
      estimatedBudget: oldData.selectedSolution?.estimatedCost || oldData.solution?.estimatedCost || 0
    }
  }

  /**
   * تحويل إلى مقترح مبسط
   */
  private static mapToSimplifiedProposal(oldData: any): SimplifiedProposalData {
    return {
      // معلومات أساسية
      title: oldData.projectName || oldData.projectTitle || '',
      description: oldData.projectDescription || oldData.problemDescription || '',
      department: oldData.responsibleDepartment || oldData.section || '',
      submitterName: oldData.teamLeader?.name || '',
      submitterEmail: oldData.teamLeader?.email || '',
      submitterPhone: oldData.teamLeader?.phone || '',

      // وصف المشكلة
      problemDescription: oldData.problemDescription || '',
      currentImpact: oldData.currentValue ? `القيمة الحالية: ${oldData.currentValue} ${oldData.unit || ''}` : '',
      affectedAreas: this.extractAffectedAreas(oldData),
      urgencyLevel: oldData.priority || 'medium',

      // الحل المقترح
      proposedSolutions: this.mapSuggestedSolutions(oldData.suggestedSolutions || []),
      recommendedSolution: oldData.recommendations || '',
      expectedBenefits: oldData.selectedSolution?.expectedBenefits || '',
      nextSteps: this.mapNextSteps(oldData.nextSteps || [])
    }
  }

  /**
   * تحويل من النظام المبسط إلى القديم
   */
  static mapSimplifiedToOld(
    simplifiedData: SimplifiedFormData,
    targetType: 'enhanced_improvement' | 'quick_win' | 'suggestion'
  ): Partial<UnifiedFormData> {
    
    const baseMapping = this.createBaseMapping(simplifiedData)
    
    switch (targetType) {
      case 'enhanced_improvement':
        return this.mapToEnhancedImprovement(simplifiedData, baseMapping)
      case 'quick_win':
        return this.mapToQuickWin(simplifiedData, baseMapping)
      case 'suggestion':
        return this.mapToSuggestion(simplifiedData, baseMapping)
      default:
        return baseMapping
    }
  }

  /**
   * إنشاء التطابق الأساسي
   */
  private static createBaseMapping(data: SimplifiedFormData): any {
    const isProject = 'projectManager' in data
    
    return {
      // معلومات أساسية
      problemDescription: data.problemDescription,
      indicatorName: isProject ? 'مؤشر المشروع' : 'مؤشر المقترح',
      currentValue: 0,
      targetValue: 100,
      improvementDirection: 'increase',
      unit: 'نسبة مئوية',
      dataSource: 'بيانات المشروع',
      measurementMethod: 'قياس دوري',
      calculatedGap: 100,
      attachments: [],

      // تنظيم الفريق
      responsibleDepartment: data.department,
      teamLeader: isProject ? {
        name: (data as SimplifiedProjectData).projectManager.name,
        phone: (data as SimplifiedProjectData).projectManager.phone,
        email: (data as SimplifiedProjectData).projectManager.email
      } : {
        name: (data as SimplifiedProposalData).submitterName,
        phone: (data as SimplifiedProposalData).submitterPhone,
        email: (data as SimplifiedProposalData).submitterEmail
      },
      teamMembers: isProject ? (data as SimplifiedProjectData).teamMembers : []
    }
  }

  /**
   * دوال مساعدة للتحويل
   */
  private static mapTasks(tasks: any[]): SimplifiedProjectData['mainTasks'] {
    return tasks.map((task, index) => ({
      id: task.id || `task-${index}`,
      title: task.title || task.description || `مهمة ${index + 1}`,
      description: task.description || task.details || '',
      duration: task.duration || task.estimatedTime || 'غير محدد'
    }))
  }

  private static mapTeamMembers(members: any[]): SimplifiedProjectData['teamMembers'] {
    return members.map((member, index) => ({
      id: member.id || `member-${index}`,
      name: member.name || '',
      role: member.role || member.position || '',
      department: member.department || ''
    }))
  }

  private static mapResources(resources: any[]): string {
    if (Array.isArray(resources)) {
      return resources.map(r => 
        typeof r === 'string' ? r : r.description || r.type || ''
      ).join('\n')
    }
    return typeof resources === 'string' ? resources : ''
  }

  private static extractAffectedAreas(data: any): string[] {
    const areas = []
    if (data.responsibleDepartment) areas.push(data.responsibleDepartment)
    if (data.processScope) areas.push(data.processScope)
    return areas
  }

  private static mapSuggestedSolutions(solutions: any[]): SimplifiedProposalData['proposedSolutions'] {
    return solutions.map((solution, index) => ({
      id: solution.id || `solution-${index}`,
      title: solution.title || `حل ${index + 1}`,
      description: solution.description || '',
      pros: solution.advantages || solution.pros || '',
      cons: solution.disadvantages || solution.cons || '',
      estimatedCost: solution.estimatedCost || 0,
      implementationTime: solution.implementationTime || solution.duration || ''
    }))
  }

  private static mapNextSteps(steps: any[]): string {
    if (Array.isArray(steps)) {
      return steps.map((step, index) => 
        `${index + 1}. ${typeof step === 'string' ? step : step.description || step.title || ''}`
      ).join('\n')
    }
    return typeof steps === 'string' ? steps : ''
  }

  private static mapToEnhancedImprovement(data: SimplifiedFormData, base: any): any {
    const isProject = 'projectManager' in data
    
    return {
      ...base,
      projectName: data.title,
      projectDescription: data.description,
      startDate: isProject ? (data as SimplifiedProjectData).startDate : '',
      endDate: isProject ? (data as SimplifiedProjectData).endDate : '',
      priority: isProject ? (data as SimplifiedProjectData).priority : (data as SimplifiedProposalData).urgencyLevel,
      
      selectedSolution: {
        description: isProject ? (data as SimplifiedProjectData).proposedSolution : '',
        justification: 'تم التحويل من النظام المبسط',
        expectedBenefits: isProject ? (data as SimplifiedProjectData).expectedBenefits : (data as SimplifiedProposalData).expectedBenefits,
        estimatedCost: isProject ? (data as SimplifiedProjectData).estimatedBudget : 0,
        implementationTime: ''
      },
      
      projectTasks: isProject ? (data as SimplifiedProjectData).mainTasks : [],
      requiredResources: isProject ? [(data as SimplifiedProjectData).requiredResources] : [],
      risks: []
    }
  }

  private static mapToQuickWin(data: SimplifiedFormData, base: any): any {
    const isProject = 'projectManager' in data
    
    return {
      ...base,
      projectTitle: data.title,
      section: data.department,
      projectExecutor: isProject ? {
        name: (data as SimplifiedProjectData).projectManager.name,
        phone: (data as SimplifiedProjectData).projectManager.phone,
        email: (data as SimplifiedProjectData).projectManager.email,
        startDate: (data as SimplifiedProjectData).startDate
      } : {
        name: (data as SimplifiedProposalData).submitterName,
        phone: (data as SimplifiedProposalData).submitterPhone,
        email: (data as SimplifiedProposalData).submitterEmail
      },
      
      solution: {
        description: isProject ? (data as SimplifiedProjectData).proposedSolution : '',
        tasks: isProject ? (data as SimplifiedProjectData).mainTasks.map(t => t.title) : [],
        implementationWeeks: 4,
        estimatedCost: isProject ? (data as SimplifiedProjectData).estimatedBudget : 0
      }
    }
  }

  private static mapToSuggestion(data: SimplifiedFormData, base: any): any {
    const isProposal = 'proposedSolutions' in data
    
    return {
      ...base,
      suggestedSolutions: isProposal ? (data as SimplifiedProposalData).proposedSolutions : [],
      recommendations: isProposal ? (data as SimplifiedProposalData).recommendedSolution : '',
      nextSteps: isProposal ? (data as SimplifiedProposalData).nextSteps.split('\n') : []
    }
  }

  /**
   * دمج البيانات من النظامين
   */
  static combineFormData(
    simplifiedData: SimplifiedFormData,
    legacyData?: any
  ): CombinedFormData {
    return {
      ...simplifiedData,
      compatibility: {
        originalFormType: legacyData?.formType || 'simplified',
        formVersion: '2.0',
        legacyData: legacyData ? {
          indicators: legacyData.advancedIndicators,
          rootCauseAnalysis: legacyData.rootCauseAnalysis,
          riskManagement: legacyData.risks,
          processDetails: legacyData.processDetails,
          attachments: legacyData.attachments
        } : undefined
      }
    }
  }

  /**
   * استخراج البيانات القديمة من البيانات المدمجة
   */
  static extractLegacyData(combinedData: CombinedFormData): any {
    return combinedData.compatibility?.legacyData || null
  }

  /**
   * التحقق من توافق البيانات
   */
  static validateCompatibility(data: any): {
    isCompatible: boolean
    missingFields: string[]
    warnings: string[]
  } {
    const missingFields: string[] = []
    const warnings: string[] = []

    // التحقق من الحقول الأساسية
    if (!data.title && !data.projectName && !data.projectTitle) {
      missingFields.push('العنوان')
    }
    
    if (!data.description && !data.projectDescription && !data.problemDescription) {
      missingFields.push('الوصف')
    }

    if (!data.department && !data.responsibleDepartment && !data.section) {
      missingFields.push('القسم')
    }

    // تحذيرات للبيانات المفقودة
    if (!data.indicators && !data.indicatorName) {
      warnings.push('المؤشرات المتقدمة غير متوفرة')
    }

    if (!data.risks && !data.riskManagement) {
      warnings.push('تحليل المخاطر غير متوفر')
    }

    return {
      isCompatible: missingFields.length === 0,
      missingFields,
      warnings
    }
  }
}
