'use client'

import React from 'react'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { WorkflowComparison } from '@/components/shared/WorkflowComparison'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useRouter } from 'next/navigation'
import { 
  ArrowRight, 
  TrendingUp, 
  Users, 
  Clock, 
  Target,
  CheckCircle,
  Star,
  Zap
} from 'lucide-react'

export default function WorkflowComparisonPage() {
  const router = useRouter()

  const statistics = [
    {
      title: 'تقليل الخطوات',
      oldValue: '9 مراحل',
      newValue: '4-5 مراحل',
      improvement: '44-56%',
      icon: Target,
      color: 'blue'
    },
    {
      title: 'توفير الوقت',
      oldValue: '45-60 دقيقة',
      newValue: '15-25 دقيقة',
      improvement: '60%',
      icon: Clock,
      color: 'green'
    },
    {
      title: 'سهولة الاستخدام',
      oldValue: 'معقد',
      newValue: 'بسيط وواضح',
      improvement: '80%',
      icon: Users,
      color: 'purple'
    },
    {
      title: 'معدل الإكمال',
      oldValue: '65%',
      newValue: '90%',
      improvement: '25%',
      icon: TrendingUp,
      color: 'orange'
    }
  ]

  const features = [
    {
      title: 'اختيار نوع الطلب',
      description: 'تمييز واضح بين المشاريع والمقترحات من البداية',
      icon: Target,
      status: 'new'
    },
    {
      title: 'مراحل مبسطة',
      description: 'تقليل عدد المراحل وتبسيط المحتوى',
      icon: CheckCircle,
      status: 'improved'
    },
    {
      title: 'واجهة محسنة',
      description: 'تصميم أكثر وضوحاً وسهولة في الاستخدام',
      icon: Star,
      status: 'improved'
    },
    {
      title: 'حفظ تلقائي',
      description: 'حفظ المسودات تلقائياً أثناء التعبئة',
      icon: Zap,
      status: 'enhanced'
    }
  ]

  const getColorClasses = (color: string) => ({
    bg: {
      blue: 'bg-blue-50 border-blue-200',
      green: 'bg-green-50 border-green-200',
      purple: 'bg-purple-50 border-purple-200',
      orange: 'bg-orange-50 border-orange-200'
    }[color],
    text: {
      blue: 'text-blue-900',
      green: 'text-green-900',
      purple: 'text-purple-900',
      orange: 'text-orange-900'
    }[color],
    icon: {
      blue: 'text-blue-600 bg-blue-100',
      green: 'text-green-600 bg-green-100',
      purple: 'text-purple-600 bg-purple-100',
      orange: 'text-orange-600 bg-orange-100'
    }[color]
  })

  const getStatusBadge = (status: string) => {
    const badges = {
      new: { label: 'جديد', color: 'bg-green-100 text-green-800' },
      improved: { label: 'محسن', color: 'bg-blue-100 text-blue-800' },
      enhanced: { label: 'متطور', color: 'bg-purple-100 text-purple-800' }
    }
    return badges[status as keyof typeof badges] || badges.improved
  }

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto space-y-12">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            تحسينات سير العمل الجديد
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            مقارنة شاملة بين النظام السابق والجديد المبسط
          </p>
          
          <div className="flex justify-center gap-4">
            <Button
              onClick={() => router.push('/requests/new-simplified')}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3"
            >
              جرب النظام الجديد
              <ArrowRight className="w-4 h-4 mr-2" />
            </Button>
            <Button
              onClick={() => router.push('/requests/new')}
              variant="ghost"
              className="px-6 py-3"
            >
              النظام المتقدم
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statistics.map((stat, index) => {
            const Icon = stat.icon
            const colors = getColorClasses(stat.color)
            
            return (
              <Card key={index} className={`p-6 ${colors.bg}`}>
                <div className="text-center">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full mb-4 ${colors.icon}`}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <h3 className={`font-semibold mb-2 ${colors.text}`}>
                    {stat.title}
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">السابق:</span>
                      <span className="text-red-600 font-medium">{stat.oldValue}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">الجديد:</span>
                      <span className="text-green-600 font-medium">{stat.newValue}</span>
                    </div>
                    <div className="pt-2 border-t border-gray-200">
                      <span className="text-xs text-gray-500">تحسن بنسبة </span>
                      <span className={`font-bold ${colors.text}`}>{stat.improvement}</span>
                    </div>
                  </div>
                </div>
              </Card>
            )
          })}
        </div>

        {/* Main Comparison */}
        <WorkflowComparison />

        {/* Features */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            الميزات الجديدة والمحسنة
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {features.map((feature, index) => {
              const Icon = feature.icon
              const badge = getStatusBadge(feature.status)
              
              return (
                <Card key={index} className="p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Icon className="w-5 h-5 text-blue-600" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold text-gray-900">
                          {feature.title}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${badge.color}`}>
                          {badge.label}
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>
        </div>

        {/* User Feedback */}
        <Card className="p-8 bg-gradient-to-r from-blue-50 to-green-50 border-blue-200">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              ملاحظات المستخدمين
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-current" />
                    ))}
                  </div>
                </div>
                <p className="text-gray-700 italic">
                  "النظام الجديد أسرع وأسهل بكثير من السابق"
                </p>
                <p className="text-gray-500 mt-2">- مدير مشروع</p>
              </div>
              
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-current" />
                    ))}
                  </div>
                </div>
                <p className="text-gray-700 italic">
                  "أخيراً أصبح الفرق واضح بين المشروع والمقترح"
                </p>
                <p className="text-gray-500 mt-2">- موظف جودة</p>
              </div>
              
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-current" />
                    ))}
                  </div>
                </div>
                <p className="text-gray-700 italic">
                  "الواجهة الجديدة بديهية ولا تحتاج تدريب"
                </p>
                <p className="text-gray-500 mt-2">- مستخدم جديد</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Call to Action */}
        <div className="text-center bg-gradient-to-r from-green-600 to-blue-600 rounded-lg p-8 text-white">
          <h2 className="text-2xl font-bold mb-4">
            ابدأ باستخدام النظام الجديد اليوم
          </h2>
          <p className="text-green-100 mb-6">
            استفد من التحسينات الجديدة ووفر وقتك وجهدك
          </p>
          <div className="flex justify-center gap-4">
            <Button
              onClick={() => router.push('/requests/new-simplified')}
              className="bg-white text-green-600 hover:bg-gray-100 px-6 py-3"
            >
              إنشاء طلب مبسط
              <Zap className="w-4 h-4 mr-2" />
            </Button>
            <Button
              onClick={() => router.push('/workflow')}
              variant="ghost"
              className="text-white border-white hover:bg-white hover:text-green-600 px-6 py-3"
            >
              دليل سير العمل
            </Button>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}
