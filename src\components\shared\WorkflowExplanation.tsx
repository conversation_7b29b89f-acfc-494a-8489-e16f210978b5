'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  FileText, 
  Settings, 
  CheckCircle, 
  ArrowRight, 
  Info,
  ChevronDown,
  ChevronUp,
  Clock,
  Users,
  Target
} from 'lucide-react'

interface WorkflowExplanationProps {
  className?: string
  showDetailed?: boolean
}

export function WorkflowExplanation({ className = '', showDetailed = false }: WorkflowExplanationProps) {
  const [isExpanded, setIsExpanded] = useState(showDetailed)

  const workflowSteps = [
    {
      id: 1,
      title: 'إنشاء المقترح',
      description: 'يقوم المستخدم بإنشاء مقترح/طلب مشروع',
      icon: FileText,
      color: 'blue',
      status: 'proposal',
      details: [
        'تعبئة النموذج الموحد',
        'حفظ المسودة تلقائياً',
        'إرسال الطلب للمراجعة'
      ]
    },
    {
      id: 2,
      title: 'مراجعة وموافقة',
      description: 'مراجعة المقترح من قبل المسؤولين',
      icon: Users,
      color: 'yellow',
      status: 'review',
      details: [
        'مراجعة من قبل مدير القسم',
        'تقييم الجدوى والأولوية',
        'الموافقة أو الرفض مع التبرير'
      ]
    },
    {
      id: 3,
      title: 'تحويل إلى مشروع',
      description: 'تحويل المقترح المعتمد إلى مشروع فعلي',
      icon: Settings,
      color: 'green',
      status: 'project',
      details: [
        'إنشاء مشروع في النظام',
        'تعيين مدير المشروع',
        'تحديد الموارد والجدول الزمني'
      ]
    },
    {
      id: 4,
      title: 'تنفيذ المشروع',
      description: 'تنفيذ ومتابعة المشروع',
      icon: Target,
      color: 'purple',
      status: 'execution',
      details: [
        'تنفيذ المهام حسب الخطة',
        'متابعة التقدم والمؤشرات',
        'إدارة المخاطر والتغييرات'
      ]
    }
  ]

  const differences = [
    {
      aspect: 'التعريف',
      proposal: 'فكرة أو طلب لتحسين أو حل مشكلة',
      project: 'مبادرة معتمدة قيد التنفيذ'
    },
    {
      aspect: 'الحالة',
      proposal: 'مسودة، مرسل، قيد المراجعة، معتمد، مرفوض',
      project: 'تخطيط، قيد التنفيذ، معلق، مكتمل، ملغي'
    },
    {
      aspect: 'المسؤولية',
      proposal: 'مقدم الطلب ومراجع النظام',
      project: 'مدير المشروع وفريق العمل'
    },
    {
      aspect: 'التتبع',
      proposal: 'تتبع حالة الموافقة والمراجعة',
      project: 'تتبع التقدم والمؤشرات والمهام'
    }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* العنوان الرئيسي */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-green-50 border-blue-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Info className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                الفرق بين المقترح والمشروع
              </h2>
              <p className="text-gray-600 mt-1">
                فهم سير العمل من الفكرة إلى التنفيذ
              </p>
            </div>
          </div>
          <Button
            variant="secondary"
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-2"
          >
            {isExpanded ? (
              <>
                <ChevronUp className="h-4 w-4" />
                إخفاء التفاصيل
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4" />
                عرض التفاصيل
              </>
            )}
          </Button>
        </div>
      </Card>

      {/* سير العمل */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Clock className="h-5 w-5 text-blue-600" />
          سير العمل
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {workflowSteps.map((step, index) => (
            <div key={step.id} className="relative">
              <div className={`
                p-4 rounded-lg border-2 transition-all duration-200
                ${step.color === 'blue' ? 'border-blue-200 bg-blue-50' : ''}
                ${step.color === 'yellow' ? 'border-yellow-200 bg-yellow-50' : ''}
                ${step.color === 'green' ? 'border-green-200 bg-green-50' : ''}
                ${step.color === 'purple' ? 'border-purple-200 bg-purple-50' : ''}
              `}>
                <div className="flex items-center gap-3 mb-2">
                  <div className={`
                    p-2 rounded-full
                    ${step.color === 'blue' ? 'bg-blue-100 text-blue-600' : ''}
                    ${step.color === 'yellow' ? 'bg-yellow-100 text-yellow-600' : ''}
                    ${step.color === 'green' ? 'bg-green-100 text-green-600' : ''}
                    ${step.color === 'purple' ? 'bg-purple-100 text-purple-600' : ''}
                  `}>
                    <step.icon className="h-4 w-4" />
                  </div>
                  <span className="text-sm font-medium text-gray-500">
                    الخطوة {step.id}
                  </span>
                </div>
                
                <h4 className="font-semibold text-gray-900 mb-1">
                  {step.title}
                </h4>
                <p className="text-sm text-gray-600 mb-3">
                  {step.description}
                </p>

                {isExpanded && (
                  <ul className="space-y-1">
                    {step.details.map((detail, idx) => (
                      <li key={idx} className="text-xs text-gray-500 flex items-center gap-1">
                        <div className="w-1 h-1 bg-gray-400 rounded-full" />
                        {detail}
                      </li>
                    ))}
                  </ul>
                )}
              </div>

              {/* السهم */}
              {index < workflowSteps.length - 1 && (
                <div className="hidden md:block absolute top-1/2 -right-2 transform -translate-y-1/2 z-10">
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* جدول المقارنة */}
      {isExpanded && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">
            مقارنة تفصيلية
          </h3>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b-2 border-gray-200">
                  <th className="text-right p-3 font-semibold text-gray-900">
                    الجانب
                  </th>
                  <th className="text-right p-3 font-semibold text-blue-600">
                    المقترح (Proposal)
                  </th>
                  <th className="text-right p-3 font-semibold text-green-600">
                    المشروع (Project)
                  </th>
                </tr>
              </thead>
              <tbody>
                {differences.map((diff, index) => (
                  <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="p-3 font-medium text-gray-900">
                      {diff.aspect}
                    </td>
                    <td className="p-3 text-gray-600">
                      {diff.proposal}
                    </td>
                    <td className="p-3 text-gray-600">
                      {diff.project}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      )}

      {/* ملاحظات مهمة */}
      <Card className="p-6 bg-amber-50 border-amber-200">
        <h3 className="text-lg font-semibold mb-3 text-amber-800">
          ملاحظات مهمة
        </h3>
        <ul className="space-y-2 text-amber-700">
          <li className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 mt-0.5 text-amber-600" />
            <span className="text-sm">
              كل مشروع يبدأ كمقترح ويجب الموافقة عليه أولاً
            </span>
          </li>
          <li className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 mt-0.5 text-amber-600" />
            <span className="text-sm">
              المقترحات المرفوضة لا تتحول إلى مشاريع
            </span>
          </li>
          <li className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 mt-0.5 text-amber-600" />
            <span className="text-sm">
              يمكن حفظ المقترحات كمسودات والعودة إليها لاحقاً
            </span>
          </li>
        </ul>
      </Card>
    </div>
  )
}
