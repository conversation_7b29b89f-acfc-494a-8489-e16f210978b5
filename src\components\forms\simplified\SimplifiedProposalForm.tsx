'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Input, Textarea, Select } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'
import { 
  ChevronRight, 
  ChevronLeft, 
  Save, 
  Send,
  Plus,
  Trash2,
  Lightbulb,
  MessageSquare,
  CheckCircle,
  FileText
} from 'lucide-react'

interface SimplifiedProposalFormProps {
  currentStep: number
  onStepChange: (step: number) => void
  onSubmit: (data: any) => void
  onSaveDraft: (data: any) => void
  isLoading: boolean
}

interface ProposalData {
  // Step 1: Basic Info
  title: string
  description: string
  department: string
  submitterName: string
  submitterEmail: string
  submitterPhone: string
  
  // Step 2: Problem Description
  problemDescription: string
  currentImpact: string
  affectedAreas: string[]
  urgencyLevel: 'low' | 'medium' | 'high' | 'urgent'
  
  // Step 3: Proposed Solution
  proposedSolutions: Array<{
    id: string
    title: string
    description: string
    pros: string
    cons: string
    estimatedCost: number
    implementationTime: string
  }>
  recommendedSolution: string
  expectedBenefits: string
  nextSteps: string
}

export function SimplifiedProposalForm({
  currentStep,
  onStepChange,
  onSubmit,
  onSaveDraft,
  isLoading
}: SimplifiedProposalFormProps) {
  const [formData, setFormData] = useState<ProposalData>({
    title: '',
    description: '',
    department: '',
    submitterName: '',
    submitterEmail: '',
    submitterPhone: '',
    problemDescription: '',
    currentImpact: '',
    affectedAreas: [],
    urgencyLevel: 'medium',
    proposedSolutions: [],
    recommendedSolution: '',
    expectedBenefits: '',
    nextSteps: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const departments = [
    { value: 'it', label: 'تقنية المعلومات' },
    { value: 'hr', label: 'الموارد البشرية' },
    { value: 'finance', label: 'المالية' },
    { value: 'operations', label: 'العمليات' },
    { value: 'marketing', label: 'التسويق' },
    { value: 'quality', label: 'الجودة' }
  ]

  const urgencyOptions = [
    { value: 'low', label: 'منخفضة', color: 'text-green-600' },
    { value: 'medium', label: 'متوسطة', color: 'text-yellow-600' },
    { value: 'high', label: 'عالية', color: 'text-orange-600' },
    { value: 'urgent', label: 'عاجلة', color: 'text-red-600' }
  ]

  const areaOptions = [
    { value: 'customer_service', label: 'خدمة العملاء' },
    { value: 'internal_processes', label: 'العمليات الداخلية' },
    { value: 'technology', label: 'التقنية' },
    { value: 'finance', label: 'المالية' },
    { value: 'hr', label: 'الموارد البشرية' },
    { value: 'quality', label: 'الجودة' },
    { value: 'safety', label: 'السلامة' },
    { value: 'environment', label: 'البيئة' }
  ]

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const addSolution = () => {
    const newSolution = {
      id: Date.now().toString(),
      title: '',
      description: '',
      pros: '',
      cons: '',
      estimatedCost: 0,
      implementationTime: ''
    }
    setFormData(prev => ({
      ...prev,
      proposedSolutions: [...prev.proposedSolutions, newSolution]
    }))
  }

  const updateSolution = (solutionId: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      proposedSolutions: prev.proposedSolutions.map(solution =>
        solution.id === solutionId ? { ...solution, [field]: value } : solution
      )
    }))
  }

  const removeSolution = (solutionId: string) => {
    setFormData(prev => ({
      ...prev,
      proposedSolutions: prev.proposedSolutions.filter(solution => solution.id !== solutionId)
    }))
  }

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {}

    switch (step) {
      case 1:
        if (!formData.title.trim()) newErrors.title = 'عنوان المقترح مطلوب'
        if (!formData.description.trim()) newErrors.description = 'وصف المقترح مطلوب'
        if (!formData.department) newErrors.department = 'القسم مطلوب'
        if (!formData.submitterName.trim()) newErrors.submitterName = 'اسم مقدم المقترح مطلوب'
        if (!formData.submitterEmail.trim()) newErrors.submitterEmail = 'البريد الإلكتروني مطلوب'
        break
      case 2:
        if (!formData.problemDescription.trim()) newErrors.problemDescription = 'وصف المشكلة مطلوب'
        if (!formData.currentImpact.trim()) newErrors.currentImpact = 'التأثير الحالي مطلوب'
        if (formData.affectedAreas.length === 0) newErrors.affectedAreas = 'يجب اختيار منطقة متأثرة واحدة على الأقل'
        break
      case 3:
        if (formData.proposedSolutions.length === 0) newErrors.proposedSolutions = 'يجب إضافة حل واحد على الأقل'
        if (!formData.recommendedSolution.trim()) newErrors.recommendedSolution = 'التوصية مطلوبة'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      onStepChange(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    onStepChange(currentStep - 1)
  }

  const handleSubmit = () => {
    if (validateStep(currentStep)) {
      onSubmit(formData)
    }
  }

  const handleSaveDraft = () => {
    onSaveDraft(formData)
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">معلومات أساسية</h2>
              <p className="text-gray-600">أدخل المعلومات الأساسية للمقترح</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان المقترح *
                </label>
                <Input
                  value={formData.title}
                  onChange={(e) => updateFormData('title', e.target.value)}
                  placeholder="أدخل عنوان واضح ومحدد للمقترح"
                  error={errors.title}
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف المقترح *
                </label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => updateFormData('description', e.target.value)}
                  placeholder="اكتب وصف مختصر للمقترح"
                  rows={3}
                  error={errors.description}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  القسم المختص *
                </label>
                <Select
                  value={formData.department}
                  onChange={(e) => updateFormData('department', e.target.value)}
                  options={departments}
                  placeholder="اختر القسم"
                  error={errors.department}
                />
              </div>

              <div></div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم مقدم المقترح *
                </label>
                <Input
                  value={formData.submitterName}
                  onChange={(e) => updateFormData('submitterName', e.target.value)}
                  placeholder="الاسم الكامل"
                  error={errors.submitterName}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني *
                </label>
                <Input
                  type="email"
                  value={formData.submitterEmail}
                  onChange={(e) => updateFormData('submitterEmail', e.target.value)}
                  placeholder="<EMAIL>"
                  error={errors.submitterEmail}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رقم الهاتف
                </label>
                <Input
                  value={formData.submitterPhone}
                  onChange={(e) => updateFormData('submitterPhone', e.target.value)}
                  placeholder="05xxxxxxxx"
                />
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">وصف المشكلة</h2>
              <p className="text-gray-600">اشرح المشكلة أو الفرصة التي يعالجها المقترح</p>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف المشكلة أو الفرصة *
                </label>
                <Textarea
                  value={formData.problemDescription}
                  onChange={(e) => updateFormData('problemDescription', e.target.value)}
                  placeholder="اشرح المشكلة الحالية أو الفرصة التحسينية بالتفصيل"
                  rows={4}
                  error={errors.problemDescription}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التأثير الحالي *
                </label>
                <Textarea
                  value={formData.currentImpact}
                  onChange={(e) => updateFormData('currentImpact', e.target.value)}
                  placeholder="كيف تؤثر هذه المشكلة على العمل حالياً؟"
                  rows={3}
                  error={errors.currentImpact}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المناطق المتأثرة *
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {areaOptions.map((area) => (
                    <label key={area.value} className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="checkbox"
                        checked={formData.affectedAreas.includes(area.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            updateFormData('affectedAreas', [...formData.affectedAreas, area.value])
                          } else {
                            updateFormData('affectedAreas', formData.affectedAreas.filter(a => a !== area.value))
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{area.label}</span>
                    </label>
                  ))}
                </div>
                {errors.affectedAreas && (
                  <p className="text-red-600 text-sm mt-2">{errors.affectedAreas}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  مستوى الأولوية
                </label>
                <Select
                  value={formData.urgencyLevel}
                  onChange={(e) => updateFormData('urgencyLevel', e.target.value)}
                  options={urgencyOptions}
                />
              </div>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">الحل المقترح</h2>
              <p className="text-gray-600">اقترح الحلول الممكنة وحدد التوصية النهائية</p>
            </div>

            <div className="space-y-6">
              <div>
                <div className="flex justify-between items-center mb-4">
                  <label className="block text-sm font-medium text-gray-700">
                    الحلول المقترحة *
                  </label>
                  <Button
                    onClick={addSolution}
                    variant="ghost"
                    className="flex items-center gap-2 text-green-600"
                  >
                    <Plus className="w-4 h-4" />
                    إضافة حل
                  </Button>
                </div>

                {formData.proposedSolutions.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    لم يتم إضافة أي حلول بعد. اضغط "إضافة حل" لبدء إضافة الحلول المقترحة.
                  </div>
                )}

                <div className="space-y-4">
                  {formData.proposedSolutions.map((solution, index) => (
                    <Card key={solution.id} className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <h4 className="font-medium text-gray-900 flex items-center gap-2">
                          <Lightbulb className="w-5 h-5 text-yellow-500" />
                          حل {index + 1}
                        </h4>
                        <Button
                          onClick={() => removeSolution(solution.id)}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            عنوان الحل
                          </label>
                          <Input
                            value={solution.title}
                            onChange={(e) => updateSolution(solution.id, 'title', e.target.value)}
                            placeholder="عنوان مختصر للحل"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            وصف الحل
                          </label>
                          <Textarea
                            value={solution.description}
                            onChange={(e) => updateSolution(solution.id, 'description', e.target.value)}
                            placeholder="اشرح الحل بالتفصيل"
                            rows={3}
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              المزايا
                            </label>
                            <Textarea
                              value={solution.pros}
                              onChange={(e) => updateSolution(solution.id, 'pros', e.target.value)}
                              placeholder="ما هي مزايا هذا الحل؟"
                              rows={2}
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              العيوب أو التحديات
                            </label>
                            <Textarea
                              value={solution.cons}
                              onChange={(e) => updateSolution(solution.id, 'cons', e.target.value)}
                              placeholder="ما هي التحديات أو العيوب؟"
                              rows={2}
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              التكلفة المقدرة (ريال سعودي)
                            </label>
                            <Input
                              type="number"
                              value={solution.estimatedCost}
                              onChange={(e) => updateSolution(solution.id, 'estimatedCost', Number(e.target.value))}
                              placeholder="0"
                              min="0"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              وقت التنفيذ المقدر
                            </label>
                            <Input
                              value={solution.implementationTime}
                              onChange={(e) => updateSolution(solution.id, 'implementationTime', e.target.value)}
                              placeholder="مثال: شهرين"
                            />
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>

                {errors.proposedSolutions && (
                  <p className="text-red-600 text-sm mt-2">{errors.proposedSolutions}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التوصية النهائية *
                </label>
                <Textarea
                  value={formData.recommendedSolution}
                  onChange={(e) => updateFormData('recommendedSolution', e.target.value)}
                  placeholder="أي من الحلول المقترحة تنصح به ولماذا؟"
                  rows={3}
                  error={errors.recommendedSolution}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفوائد المتوقعة
                </label>
                <Textarea
                  value={formData.expectedBenefits}
                  onChange={(e) => updateFormData('expectedBenefits', e.target.value)}
                  placeholder="ما هي الفوائد المتوقعة من تطبيق هذا المقترح؟"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الخطوات التالية المقترحة
                </label>
                <Textarea
                  value={formData.nextSteps}
                  onChange={(e) => updateFormData('nextSteps', e.target.value)}
                  placeholder="ما هي الخطوات التالية المطلوبة لتنفيذ هذا المقترح؟"
                  rows={3}
                />
              </div>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">المراجعة والإرسال</h2>
              <p className="text-gray-600">راجع جميع المعلومات قبل إرسال المقترح</p>
            </div>

            <div className="space-y-6">
              {/* Proposal Summary */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  ملخص المقترح
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">العنوان:</span>
                    <p className="text-gray-900">{formData.title}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">القسم:</span>
                    <p className="text-gray-900">{departments.find(d => d.value === formData.department)?.label}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">مقدم المقترح:</span>
                    <p className="text-gray-900">{formData.submitterName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">مستوى الأولوية:</span>
                    <p className="text-gray-900">{urgencyOptions.find(u => u.value === formData.urgencyLevel)?.label}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">عدد الحلول المقترحة:</span>
                    <p className="text-gray-900">{formData.proposedSolutions.length} حل</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">المناطق المتأثرة:</span>
                    <p className="text-gray-900">{formData.affectedAreas.length} منطقة</p>
                  </div>
                </div>
              </Card>

              {/* Problem Summary */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <MessageSquare className="w-5 h-5" />
                  ملخص المشكلة
                </h3>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {formData.problemDescription}
                </p>
              </Card>

              {/* Solutions Summary */}
              {formData.proposedSolutions.length > 0 && (
                <Card className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Lightbulb className="w-5 h-5" />
                    الحلول المقترحة
                  </h3>
                  <div className="space-y-3">
                    {formData.proposedSolutions.map((solution, index) => (
                      <div key={solution.id} className="border-l-4 border-green-500 pl-4">
                        <h4 className="font-medium text-gray-900">
                          {index + 1}. {solution.title}
                        </h4>
                        <p className="text-sm text-gray-600 mt-1">
                          {solution.description}
                        </p>
                        <div className="flex gap-4 mt-2 text-xs text-gray-500">
                          <span>التكلفة: {solution.estimatedCost} ريال</span>
                          <span>الوقت: {solution.implementationTime}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              )}

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <h4 className="font-semibold text-green-900">جاهز للإرسال</h4>
                </div>
                <p className="text-green-800 text-sm">
                  تم مراجعة جميع المعلومات. سيتم إرسال المقترح للمراجعة والتقييم من قبل الفريق المختص.
                </p>
              </div>
            </div>
          </div>
        )

      default:
        return <div>خطوة غير معروفة</div>
    }
  }

  return (
    <div className="space-y-8">
      {renderStep()}

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <div className="flex gap-3">
          {currentStep > 1 && (
            <Button
              onClick={handlePrevious}
              variant="ghost"
              className="flex items-center gap-2"
            >
              <ChevronLeft className="w-4 h-4" />
              السابق
            </Button>
          )}
        </div>

        <div className="flex gap-3">
          <Button
            onClick={handleSaveDraft}
            variant="ghost"
            className="flex items-center gap-2"
          >
            <Save className="w-4 h-4" />
            حفظ مسودة
          </Button>

          {currentStep < 4 ? (
            <Button
              onClick={handleNext}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
            >
              التالي
              <ChevronRight className="w-4 h-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={isLoading}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري الإرسال...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4" />
                  إرسال المقترح
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
