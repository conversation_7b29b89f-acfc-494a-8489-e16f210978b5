'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Input, Textarea, Select } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'
import { 
  ChevronRight, 
  ChevronLeft, 
  Save, 
  Send,
  Plus,
  Trash2,
  Calendar,
  User,
  Target,
  CheckCircle
} from 'lucide-react'

interface SimplifiedProjectFormProps {
  currentStep: number
  onStepChange: (step: number) => void
  onSubmit: (data: any) => void
  onSaveDraft: (data: any) => void
  isLoading: boolean
}

interface ProjectData {
  // Step 1: Basic Info
  title: string
  description: string
  department: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  startDate: string
  endDate: string
  
  // Step 2: Problem & Goal
  problemDescription: string
  currentSituation: string
  targetGoal: string
  successCriteria: string
  
  // Step 3: Solution & Tasks
  proposedSolution: string
  mainTasks: Array<{
    id: string
    title: string
    description: string
    duration: string
  }>
  expectedBenefits: string
  
  // Step 4: Team & Resources
  projectManager: {
    name: string
    email: string
    phone: string
  }
  teamMembers: Array<{
    id: string
    name: string
    role: string
    department: string
  }>
  requiredResources: string
  estimatedBudget: number
}

export function SimplifiedProjectForm({
  currentStep,
  onStepChange,
  onSubmit,
  onSaveDraft,
  isLoading
}: SimplifiedProjectFormProps) {
  const [formData, setFormData] = useState<ProjectData>({
    title: '',
    description: '',
    department: '',
    priority: 'medium',
    startDate: '',
    endDate: '',
    problemDescription: '',
    currentSituation: '',
    targetGoal: '',
    successCriteria: '',
    proposedSolution: '',
    mainTasks: [],
    expectedBenefits: '',
    projectManager: { name: '', email: '', phone: '' },
    teamMembers: [],
    requiredResources: '',
    estimatedBudget: 0
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const departments = [
    { value: 'it', label: 'تقنية المعلومات' },
    { value: 'hr', label: 'الموارد البشرية' },
    { value: 'finance', label: 'المالية' },
    { value: 'operations', label: 'العمليات' },
    { value: 'marketing', label: 'التسويق' },
    { value: 'quality', label: 'الجودة' }
  ]

  const priorityOptions = [
    { value: 'low', label: 'منخفضة', color: 'text-green-600' },
    { value: 'medium', label: 'متوسطة', color: 'text-yellow-600' },
    { value: 'high', label: 'عالية', color: 'text-orange-600' },
    { value: 'urgent', label: 'عاجلة', color: 'text-red-600' }
  ]

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const addTask = () => {
    const newTask = {
      id: Date.now().toString(),
      title: '',
      description: '',
      duration: ''
    }
    setFormData(prev => ({
      ...prev,
      mainTasks: [...prev.mainTasks, newTask]
    }))
  }

  const updateTask = (taskId: string, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      mainTasks: prev.mainTasks.map(task =>
        task.id === taskId ? { ...task, [field]: value } : task
      )
    }))
  }

  const removeTask = (taskId: string) => {
    setFormData(prev => ({
      ...prev,
      mainTasks: prev.mainTasks.filter(task => task.id !== taskId)
    }))
  }

  const addTeamMember = () => {
    const newMember = {
      id: Date.now().toString(),
      name: '',
      role: '',
      department: ''
    }
    setFormData(prev => ({
      ...prev,
      teamMembers: [...prev.teamMembers, newMember]
    }))
  }

  const updateTeamMember = (memberId: string, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      teamMembers: prev.teamMembers.map(member =>
        member.id === memberId ? { ...member, [field]: value } : member
      )
    }))
  }

  const removeTeamMember = (memberId: string) => {
    setFormData(prev => ({
      ...prev,
      teamMembers: prev.teamMembers.filter(member => member.id !== memberId)
    }))
  }

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {}

    switch (step) {
      case 1:
        if (!formData.title.trim()) newErrors.title = 'عنوان المشروع مطلوب'
        if (!formData.description.trim()) newErrors.description = 'وصف المشروع مطلوب'
        if (!formData.department) newErrors.department = 'القسم مطلوب'
        if (!formData.startDate) newErrors.startDate = 'تاريخ البداية مطلوب'
        if (!formData.endDate) newErrors.endDate = 'تاريخ النهاية مطلوب'
        if (formData.startDate && formData.endDate && new Date(formData.startDate) >= new Date(formData.endDate)) {
          newErrors.endDate = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية'
        }
        break
      case 2:
        if (!formData.problemDescription.trim()) newErrors.problemDescription = 'وصف المشكلة مطلوب'
        if (!formData.targetGoal.trim()) newErrors.targetGoal = 'الهدف المطلوب مطلوب'
        break
      case 3:
        if (!formData.proposedSolution.trim()) newErrors.proposedSolution = 'الحل المقترح مطلوب'
        if (formData.mainTasks.length === 0) newErrors.mainTasks = 'يجب إضافة مهمة واحدة على الأقل'
        break
      case 4:
        if (!formData.projectManager.name.trim()) newErrors['projectManager.name'] = 'اسم مدير المشروع مطلوب'
        if (!formData.projectManager.email.trim()) newErrors['projectManager.email'] = 'بريد مدير المشروع مطلوب'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      onStepChange(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    onStepChange(currentStep - 1)
  }

  const handleSubmit = () => {
    if (validateStep(currentStep)) {
      onSubmit(formData)
    }
  }

  const handleSaveDraft = () => {
    onSaveDraft(formData)
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">معلومات أساسية</h2>
              <p className="text-gray-600">أدخل المعلومات الأساسية للمشروع</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان المشروع *
                </label>
                <Input
                  value={formData.title}
                  onChange={(e) => updateFormData('title', e.target.value)}
                  placeholder="أدخل عنوان واضح ومحدد للمشروع"
                  error={errors.title}
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف المشروع *
                </label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => updateFormData('description', e.target.value)}
                  placeholder="اكتب وصف مفصل للمشروع وأهدافه"
                  rows={4}
                  error={errors.description}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  القسم المسؤول *
                </label>
                <Select
                  value={formData.department}
                  onChange={(e) => updateFormData('department', e.target.value)}
                  options={departments}
                  placeholder="اختر القسم"
                  error={errors.department}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الأولوية
                </label>
                <Select
                  value={formData.priority}
                  onChange={(e) => updateFormData('priority', e.target.value)}
                  options={priorityOptions}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تاريخ البداية *
                </label>
                <Input
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => updateFormData('startDate', e.target.value)}
                  error={errors.startDate}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تاريخ النهاية *
                </label>
                <Input
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => updateFormData('endDate', e.target.value)}
                  error={errors.endDate}
                />
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">المشكلة والهدف</h2>
              <p className="text-gray-600">حدد المشكلة التي يحلها المشروع والهدف المطلوب تحقيقه</p>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف المشكلة *
                </label>
                <Textarea
                  value={formData.problemDescription}
                  onChange={(e) => updateFormData('problemDescription', e.target.value)}
                  placeholder="اشرح المشكلة الحالية التي يهدف المشروع لحلها"
                  rows={4}
                  error={errors.problemDescription}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الوضع الحالي
                </label>
                <Textarea
                  value={formData.currentSituation}
                  onChange={(e) => updateFormData('currentSituation', e.target.value)}
                  placeholder="اوصف الوضع الحالي بالتفصيل"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الهدف المطلوب تحقيقه *
                </label>
                <Textarea
                  value={formData.targetGoal}
                  onChange={(e) => updateFormData('targetGoal', e.target.value)}
                  placeholder="حدد الهدف الذي تريد تحقيقه من هذا المشروع"
                  rows={3}
                  error={errors.targetGoal}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  معايير النجاح
                </label>
                <Textarea
                  value={formData.successCriteria}
                  onChange={(e) => updateFormData('successCriteria', e.target.value)}
                  placeholder="كيف ستقيس نجاح المشروع؟"
                  rows={3}
                />
              </div>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">الحل والمهام</h2>
              <p className="text-gray-600">اقترح الحل وحدد المهام الرئيسية للمشروع</p>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الحل المقترح *
                </label>
                <Textarea
                  value={formData.proposedSolution}
                  onChange={(e) => updateFormData('proposedSolution', e.target.value)}
                  placeholder="اشرح الحل المقترح بالتفصيل"
                  rows={4}
                  error={errors.proposedSolution}
                />
              </div>

              <div>
                <div className="flex justify-between items-center mb-4">
                  <label className="block text-sm font-medium text-gray-700">
                    المهام الرئيسية *
                  </label>
                  <Button
                    onClick={addTask}
                    variant="ghost"
                    className="flex items-center gap-2 text-blue-600"
                  >
                    <Plus className="w-4 h-4" />
                    إضافة مهمة
                  </Button>
                </div>

                {formData.mainTasks.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    لم يتم إضافة أي مهام بعد. اضغط "إضافة مهمة" لبدء إضافة المهام.
                  </div>
                )}

                <div className="space-y-4">
                  {formData.mainTasks.map((task, index) => (
                    <Card key={task.id} className="p-4">
                      <div className="flex justify-between items-start mb-3">
                        <h4 className="font-medium text-gray-900">مهمة {index + 1}</h4>
                        <Button
                          onClick={() => removeTask(task.id)}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            عنوان المهمة
                          </label>
                          <Input
                            value={task.title}
                            onChange={(e) => updateTask(task.id, 'title', e.target.value)}
                            placeholder="عنوان المهمة"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            المدة المتوقعة
                          </label>
                          <Input
                            value={task.duration}
                            onChange={(e) => updateTask(task.id, 'duration', e.target.value)}
                            placeholder="مثال: أسبوعين"
                          />
                        </div>

                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            وصف المهمة
                          </label>
                          <Textarea
                            value={task.description}
                            onChange={(e) => updateTask(task.id, 'description', e.target.value)}
                            placeholder="اشرح تفاصيل المهمة"
                            rows={2}
                          />
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>

                {errors.mainTasks && (
                  <p className="text-red-600 text-sm mt-2">{errors.mainTasks}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفوائد المتوقعة
                </label>
                <Textarea
                  value={formData.expectedBenefits}
                  onChange={(e) => updateFormData('expectedBenefits', e.target.value)}
                  placeholder="اذكر الفوائد والنتائج المتوقعة من المشروع"
                  rows={3}
                />
              </div>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">الفريق والموارد</h2>
              <p className="text-gray-600">حدد فريق العمل والموارد المطلوبة للمشروع</p>
            </div>

            <div className="space-y-6">
              {/* Project Manager */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <User className="w-5 h-5" />
                  مدير المشروع
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم *
                    </label>
                    <Input
                      value={formData.projectManager.name}
                      onChange={(e) => updateFormData('projectManager', {
                        ...formData.projectManager,
                        name: e.target.value
                      })}
                      placeholder="اسم مدير المشروع"
                      error={errors['projectManager.name']}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      البريد الإلكتروني *
                    </label>
                    <Input
                      type="email"
                      value={formData.projectManager.email}
                      onChange={(e) => updateFormData('projectManager', {
                        ...formData.projectManager,
                        email: e.target.value
                      })}
                      placeholder="<EMAIL>"
                      error={errors['projectManager.email']}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهاتف
                    </label>
                    <Input
                      value={formData.projectManager.phone}
                      onChange={(e) => updateFormData('projectManager', {
                        ...formData.projectManager,
                        phone: e.target.value
                      })}
                      placeholder="05xxxxxxxx"
                    />
                  </div>
                </div>
              </Card>

              {/* Team Members */}
              <div>
                <div className="flex justify-between items-center mb-4">
                  <label className="block text-sm font-medium text-gray-700">
                    أعضاء الفريق
                  </label>
                  <Button
                    onClick={addTeamMember}
                    variant="ghost"
                    className="flex items-center gap-2 text-blue-600"
                  >
                    <Plus className="w-4 h-4" />
                    إضافة عضو
                  </Button>
                </div>

                <div className="space-y-4">
                  {formData.teamMembers.map((member, index) => (
                    <Card key={member.id} className="p-4">
                      <div className="flex justify-between items-start mb-3">
                        <h4 className="font-medium text-gray-900">عضو {index + 1}</h4>
                        <Button
                          onClick={() => removeTeamMember(member.id)}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            الاسم
                          </label>
                          <Input
                            value={member.name}
                            onChange={(e) => updateTeamMember(member.id, 'name', e.target.value)}
                            placeholder="اسم العضو"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            الدور
                          </label>
                          <Input
                            value={member.role}
                            onChange={(e) => updateTeamMember(member.id, 'role', e.target.value)}
                            placeholder="مثال: مطور، محلل، مصمم"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            القسم
                          </label>
                          <Select
                            value={member.department}
                            onChange={(e) => updateTeamMember(member.id, 'department', e.target.value)}
                            options={departments}
                            placeholder="اختر القسم"
                          />
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Resources and Budget */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الموارد المطلوبة
                  </label>
                  <Textarea
                    value={formData.requiredResources}
                    onChange={(e) => updateFormData('requiredResources', e.target.value)}
                    placeholder="اذكر الموارد المطلوبة (أجهزة، برامج، مواد، إلخ)"
                    rows={4}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الميزانية المقدرة (ريال سعودي)
                  </label>
                  <Input
                    type="number"
                    value={formData.estimatedBudget}
                    onChange={(e) => updateFormData('estimatedBudget', Number(e.target.value))}
                    placeholder="0"
                    min="0"
                  />
                </div>
              </div>
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">المراجعة والإرسال</h2>
              <p className="text-gray-600">راجع جميع المعلومات قبل إرسال المشروع</p>
            </div>

            <div className="space-y-6">
              {/* Project Summary */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">ملخص المشروع</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">العنوان:</span>
                    <p className="text-gray-900">{formData.title}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">القسم:</span>
                    <p className="text-gray-900">{departments.find(d => d.value === formData.department)?.label}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">تاريخ البداية:</span>
                    <p className="text-gray-900">{formData.startDate}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">تاريخ النهاية:</span>
                    <p className="text-gray-900">{formData.endDate}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">مدير المشروع:</span>
                    <p className="text-gray-900">{formData.projectManager.name}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">عدد المهام:</span>
                    <p className="text-gray-900">{formData.mainTasks.length} مهمة</p>
                  </div>
                </div>
              </Card>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <h4 className="font-semibold text-green-900">جاهز للإرسال</h4>
                </div>
                <p className="text-green-800 text-sm">
                  تم مراجعة جميع المعلومات. يمكنك الآن إرسال المشروع أو حفظه كمسودة للمراجعة لاحقاً.
                </p>
              </div>
            </div>
          </div>
        )

      default:
        return <div>خطوة غير معروفة</div>
    }
  }

  return (
    <div className="space-y-8">
      {renderStep()}

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <div className="flex gap-3">
          {currentStep > 1 && (
            <Button
              onClick={handlePrevious}
              variant="ghost"
              className="flex items-center gap-2"
            >
              <ChevronLeft className="w-4 h-4" />
              السابق
            </Button>
          )}
        </div>

        <div className="flex gap-3">
          <Button
            onClick={handleSaveDraft}
            variant="ghost"
            className="flex items-center gap-2"
          >
            <Save className="w-4 h-4" />
            حفظ مسودة
          </Button>

          {currentStep < 5 ? (
            <Button
              onClick={handleNext}
              className="flex items-center gap-2"
            >
              التالي
              <ChevronRight className="w-4 h-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={isLoading}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري الإرسال...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4" />
                  إرسال المشروع
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
