'use client'

import React, { useState } from 'react'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useRouter } from 'next/navigation'
import { 
  TestTube, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Play,
  RotateCcw,
  FileText,
  MessageSquare,
  Zap,
  TrendingUp,
  Users,
  Target,
  AlertCircle
} from 'lucide-react'

interface TestResult {
  id: string
  name: string
  status: 'pending' | 'running' | 'passed' | 'failed'
  duration?: number
  error?: string
  details?: string
}

export default function SimplifiedWorkflowTestPage() {
  const router = useRouter()
  const [testResults, setTestResults] = useState<TestResult[]>([
    {
      id: 'navigation',
      name: 'اختبار التنقل للنظام المبسط',
      status: 'pending',
      details: 'فحص جميع روابط التنقل والصفحات الجديدة'
    },
    {
      id: 'project-form',
      name: 'اختبار نموذج المشروع المبسط',
      status: 'pending',
      details: 'فحص جميع خطوات نموذج المشروع (5 مراحل)'
    },
    {
      id: 'proposal-form',
      name: 'اختبار نموذج المقترح المبسط',
      status: 'pending',
      details: 'فحص جميع خطوات نموذج المقترح (4 مراحل)'
    },
    {
      id: 'progress-indicator',
      name: 'اختبار مؤشر التقدم',
      status: 'pending',
      details: 'فحص عمل مؤشر التقدم والتنقل بين الخطوات'
    },
    {
      id: 'interactive-guide',
      name: 'اختبار الدليل التفاعلي',
      status: 'pending',
      details: 'فحص عمل الإرشادات التفاعلية'
    },
    {
      id: 'workflow-comparison',
      name: 'اختبار صفحة المقارنة',
      status: 'pending',
      details: 'فحص صفحة مقارنة التحسينات'
    },
    {
      id: 'dashboard-enhanced',
      name: 'اختبار لوحة التحكم المحسنة',
      status: 'pending',
      details: 'فحص الواجهة الجديدة والإجراءات السريعة'
    },
    {
      id: 'form-validation',
      name: 'اختبار التحقق من البيانات',
      status: 'pending',
      details: 'فحص رسائل الخطأ والتحقق من صحة البيانات'
    }
  ])

  const [isRunningAll, setIsRunningAll] = useState(false)

  const runSingleTest = async (testId: string) => {
    setTestResults(prev => prev.map(test => 
      test.id === testId 
        ? { ...test, status: 'running' }
        : test
    ))

    // محاكاة اختبار
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000))

    const success = Math.random() > 0.2 // 80% نجاح

    setTestResults(prev => prev.map(test => 
      test.id === testId 
        ? { 
            ...test, 
            status: success ? 'passed' : 'failed',
            duration: Math.round(2000 + Math.random() * 3000),
            error: success ? undefined : 'خطأ في الاختبار - يحتاج مراجعة'
          }
        : test
    ))
  }

  const runAllTests = async () => {
    setIsRunningAll(true)
    
    for (const test of testResults) {
      await runSingleTest(test.id)
    }
    
    setIsRunningAll(false)
  }

  const resetTests = () => {
    setTestResults(prev => prev.map(test => ({
      ...test,
      status: 'pending',
      duration: undefined,
      error: undefined
    })))
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
      case 'passed':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-600" />
      default:
        return <Clock className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'border-blue-200 bg-blue-50'
      case 'passed':
        return 'border-green-200 bg-green-50'
      case 'failed':
        return 'border-red-200 bg-red-50'
      default:
        return 'border-gray-200 bg-white'
    }
  }

  const passedTests = testResults.filter(t => t.status === 'passed').length
  const failedTests = testResults.filter(t => t.status === 'failed').length
  const totalTests = testResults.length

  const testCategories = [
    {
      title: 'اختبارات التنقل والواجهة',
      icon: TrendingUp,
      tests: ['navigation', 'dashboard-enhanced', 'workflow-comparison']
    },
    {
      title: 'اختبارات النماذج',
      icon: FileText,
      tests: ['project-form', 'proposal-form', 'form-validation']
    },
    {
      title: 'اختبارات المكونات التفاعلية',
      icon: Zap,
      tests: ['progress-indicator', 'interactive-guide']
    }
  ]

  return (
    <ProtectedLayout>
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            اختبار النظام المبسط الجديد
          </h1>
          <p className="text-lg text-gray-600">
            اختبار شامل لجميع مكونات وميزات سير العمل المبسط
          </p>
        </div>

        {/* Test Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{totalTests}</div>
            <div className="text-sm text-gray-600">إجمالي الاختبارات</div>
          </Card>
          <Card className="p-4 text-center bg-green-50 border-green-200">
            <div className="text-2xl font-bold text-green-900">{passedTests}</div>
            <div className="text-sm text-green-700">نجح</div>
          </Card>
          <Card className="p-4 text-center bg-red-50 border-red-200">
            <div className="text-2xl font-bold text-red-900">{failedTests}</div>
            <div className="text-sm text-red-700">فشل</div>
          </Card>
          <Card className="p-4 text-center bg-blue-50 border-blue-200">
            <div className="text-2xl font-bold text-blue-900">
              {totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%
            </div>
            <div className="text-sm text-blue-700">معدل النجاح</div>
          </Card>
        </div>

        {/* Control Buttons */}
        <div className="flex justify-center gap-4">
          <Button
            onClick={runAllTests}
            disabled={isRunningAll}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
          >
            {isRunningAll ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                جاري التشغيل...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                تشغيل جميع الاختبارات
              </>
            )}
          </Button>
          
          <Button
            onClick={resetTests}
            variant="ghost"
            className="px-6 py-3"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            إعادة تعيين
          </Button>
        </div>

        {/* Test Categories */}
        {testCategories.map((category, categoryIndex) => {
          const Icon = category.icon
          const categoryTests = testResults.filter(test => category.tests.includes(test.id))
          
          return (
            <Card key={categoryIndex} className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Icon className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">{category.title}</h2>
              </div>
              
              <div className="space-y-3">
                {categoryTests.map((test) => (
                  <div
                    key={test.id}
                    className={`p-4 rounded-lg border transition-all duration-200 ${getStatusColor(test.status)}`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(test.status)}
                        <div>
                          <h3 className="font-medium text-gray-900">{test.name}</h3>
                          <p className="text-sm text-gray-600">{test.details}</p>
                          {test.duration && (
                            <p className="text-xs text-gray-500 mt-1">
                              مدة التنفيذ: {test.duration}ms
                            </p>
                          )}
                          {test.error && (
                            <p className="text-xs text-red-600 mt-1 flex items-center gap-1">
                              <AlertCircle className="w-3 h-3" />
                              {test.error}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      <Button
                        onClick={() => runSingleTest(test.id)}
                        disabled={test.status === 'running' || isRunningAll}
                        size="sm"
                        variant="ghost"
                      >
                        {test.status === 'running' ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          )
        })}

        {/* Quick Links for Manual Testing */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">اختبار يدوي سريع</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              onClick={() => router.push('/requests/new-simplified')}
              variant="ghost"
              className="p-4 h-auto flex flex-col items-center gap-2"
            >
              <Zap className="w-6 h-6 text-green-600" />
              <span>النظام المبسط</span>
            </Button>
            
            <Button
              onClick={() => router.push('/workflow-comparison')}
              variant="ghost"
              className="p-4 h-auto flex flex-col items-center gap-2"
            >
              <TrendingUp className="w-6 h-6 text-blue-600" />
              <span>مقارنة التحسينات</span>
            </Button>
            
            <Button
              onClick={() => router.push('/dashboard-enhanced')}
              variant="ghost"
              className="p-4 h-auto flex flex-col items-center gap-2"
            >
              <Target className="w-6 h-6 text-purple-600" />
              <span>لوحة التحكم المحسنة</span>
            </Button>
            
            <Button
              onClick={() => router.push('/workflow')}
              variant="ghost"
              className="p-4 h-auto flex flex-col items-center gap-2"
            >
              <FileText className="w-6 h-6 text-orange-600" />
              <span>دليل سير العمل</span>
            </Button>
          </div>
        </Card>
      </div>
    </ProtectedLayout>
  )
}
