'use client'

import React from 'react'
import { CheckCircle, Circle, AlertCircle } from 'lucide-react'

interface Step {
  number: number
  title: string
  description: string
  isRequired: boolean
}

interface EnhancedProgressBarProps {
  steps: Step[]
  currentStep: number
  completedSteps: number[]
  stepsWithErrors: number[]
  onStepClick?: (step: number) => void
  allowNavigation?: boolean
  className?: string
}

export function EnhancedProgressBar({
  steps,
  currentStep,
  completedSteps = [],
  stepsWithErrors = [],
  onStepClick,
  allowNavigation = false,
  className = ''
}: EnhancedProgressBarProps) {
  
  const getStepStatus = (stepNumber: number) => {
    if (stepsWithErrors.includes(stepNumber)) return 'error'
    if (completedSteps.includes(stepNumber)) return 'completed'
    if (stepNumber === currentStep) return 'current'
    if (stepNumber < currentStep) return 'visited'
    return 'upcoming'
  }

  const getStepClasses = (status: string, isRequired: boolean) => {
    const baseClasses = 'transition-all duration-200 border-2 rounded-full flex items-center justify-center font-semibold text-sm'
    
    switch (status) {
      case 'completed':
        return `${baseClasses} bg-green-600 text-white border-green-600`
      case 'current':
        return `${baseClasses} bg-blue-600 text-white border-blue-600 ring-4 ring-blue-100`
      case 'error':
        return `${baseClasses} bg-red-600 text-white border-red-600 ring-4 ring-red-100`
      case 'visited':
        return `${baseClasses} bg-gray-300 text-gray-700 border-gray-300`
      case 'upcoming':
        return `${baseClasses} ${
          isRequired 
            ? 'bg-white text-gray-500 border-gray-300' 
            : 'bg-gray-100 text-gray-400 border-gray-200'
        }`
      default:
        return baseClasses
    }
  }

  const getConnectorClasses = (fromStep: number) => {
    const fromStatus = getStepStatus(fromStep)
    const toStatus = getStepStatus(fromStep + 1)
    
    if (fromStatus === 'completed' || fromStatus === 'current') {
      return 'bg-green-600'
    } else if (fromStatus === 'error') {
      return 'bg-red-600'
    } else if (fromStatus === 'visited') {
      return 'bg-gray-300'
    }
    return 'bg-gray-200'
  }

  const handleStepClick = (stepNumber: number) => {
    if (allowNavigation && onStepClick && stepNumber <= currentStep) {
      onStepClick(stepNumber)
    }
  }

  const getStepIcon = (step: Step, status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5" />
      case 'error':
        return <AlertCircle className="w-5 h-5" />
      default:
        return <span>{step.number}</span>
    }
  }

  return (
    <div className={`w-full ${className}`}>
      {/* Desktop Progress Bar */}
      <div className="hidden md:block">
        <div className="flex items-center justify-between relative">
          {steps.map((step, index) => {
            const status = getStepStatus(step.number)
            const isClickable = allowNavigation && step.number <= currentStep

            return (
              <React.Fragment key={step.number}>
                <div className="flex flex-col items-center relative z-10">
                  {/* Step Circle */}
                  <div
                    className={`
                      w-12 h-12 mb-2 cursor-pointer
                      ${getStepClasses(status, step.isRequired)}
                      ${isClickable ? 'hover:scale-105' : ''}
                    `}
                    onClick={() => handleStepClick(step.number)}
                    title={step.description}
                  >
                    {getStepIcon(step, status)}
                  </div>
                  
                  {/* Step Label */}
                  <div className="text-center max-w-20">
                    <p className={`text-xs font-medium ${
                      status === 'current' 
                        ? 'text-blue-900' 
                        : status === 'completed' 
                          ? 'text-green-900'
                          : status === 'error'
                            ? 'text-red-900'
                            : 'text-gray-500'
                    }`}>
                      {step.title}
                    </p>
                    {!step.isRequired && (
                      <span className="text-xs text-gray-400">(اختياري)</span>
                    )}
                  </div>
                </div>

                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="flex-1 h-0.5 mx-2 relative -top-6">
                    <div className={`h-full transition-all duration-300 ${getConnectorClasses(step.number)}`} />
                  </div>
                )}
              </React.Fragment>
            )
          })}
        </div>
      </div>

      {/* Mobile Progress Bar */}
      <div className="md:hidden">
        <div className="flex items-center justify-center mb-4">
          <div className="text-center">
            <div className={`w-16 h-16 mb-2 ${getStepClasses(getStepStatus(currentStep), steps[currentStep - 1]?.isRequired || true)}`}>
              {getStepIcon(steps[currentStep - 1], getStepStatus(currentStep))}
            </div>
            <p className="text-sm font-medium text-gray-900">
              {steps[currentStep - 1]?.title}
            </p>
            <p className="text-xs text-gray-500">
              {currentStep} من {steps.length}
            </p>
          </div>
        </div>
        
        {/* Mobile Step Dots */}
        <div className="flex justify-center space-x-2 mb-4">
          {steps.map((step) => {
            const status = getStepStatus(step.number)
            return (
              <div
                key={step.number}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  status === 'completed' 
                    ? 'bg-green-600' 
                    : status === 'current'
                      ? 'bg-blue-600'
                      : status === 'error'
                        ? 'bg-red-600'
                        : 'bg-gray-200'
                }`}
              />
            )
          })}
        </div>
      </div>

      {/* Progress Percentage */}
      <div className="mt-4">
        <div className="flex justify-between text-xs text-gray-500 mb-2">
          <span>التقدم</span>
          <span>{Math.round(((currentStep - 1) / (steps.length - 1)) * 100)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-600 to-green-600 h-2 rounded-full transition-all duration-500"
            style={{
              width: `${((currentStep - 1) / (steps.length - 1)) * 100}%`
            }}
          />
        </div>
      </div>

      {/* Step Summary */}
      <div className="mt-4 text-center">
        <div className="flex justify-center space-x-4 text-xs">
          <div className="flex items-center space-x-1">
            <CheckCircle className="w-3 h-3 text-green-600" />
            <span className="text-gray-600">{completedSteps.length} مكتمل</span>
          </div>
          {stepsWithErrors.length > 0 && (
            <div className="flex items-center space-x-1">
              <AlertCircle className="w-3 h-3 text-red-600" />
              <span className="text-gray-600">{stepsWithErrors.length} يحتاج مراجعة</span>
            </div>
          )}
          <div className="flex items-center space-x-1">
            <Circle className="w-3 h-3 text-gray-400" />
            <span className="text-gray-600">{steps.length - currentStep} متبقي</span>
          </div>
        </div>
      </div>
    </div>
  )
}

// Hook لإدارة حالة التقدم
export function useProgressTracking(totalSteps: number) {
  const [completedSteps, setCompletedSteps] = React.useState<number[]>([])
  const [stepsWithErrors, setStepsWithErrors] = React.useState<number[]>([])

  const markStepCompleted = (stepNumber: number) => {
    setCompletedSteps(prev => {
      if (!prev.includes(stepNumber)) {
        return [...prev, stepNumber]
      }
      return prev
    })
    // إزالة من قائمة الأخطاء إذا كان موجود
    setStepsWithErrors(prev => prev.filter(step => step !== stepNumber))
  }

  const markStepWithError = (stepNumber: number) => {
    setStepsWithErrors(prev => {
      if (!prev.includes(stepNumber)) {
        return [...prev, stepNumber]
      }
      return prev
    })
    // إزالة من قائمة المكتملة إذا كان موجود
    setCompletedSteps(prev => prev.filter(step => step !== stepNumber))
  }

  const clearStepError = (stepNumber: number) => {
    setStepsWithErrors(prev => prev.filter(step => step !== stepNumber))
  }

  const resetProgress = () => {
    setCompletedSteps([])
    setStepsWithErrors([])
  }

  return {
    completedSteps,
    stepsWithErrors,
    markStepCompleted,
    markStepWithError,
    clearStepError,
    resetProgress
  }
}
