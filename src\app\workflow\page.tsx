'use client'

import React from 'react'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { WorkflowExplanation } from '@/components/shared/WorkflowExplanation'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  ArrowLeft, 
  BookOpen, 
  FileText, 
  Settings,
  Users,
  Target,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'

export default function WorkflowPage() {
  const statusExplanations = [
    {
      category: 'حالات المقترح',
      statuses: [
        {
          name: 'مسودة',
          description: 'المقترح محفوظ ولم يتم إرساله بعد',
          icon: FileText,
          color: 'gray'
        },
        {
          name: 'مرسل',
          description: 'تم إرسال المقترح وفي انتظار المراجعة',
          icon: Clock,
          color: 'blue'
        },
        {
          name: 'قيد المراجعة',
          description: 'يتم مراجعة المقترح من قبل المسؤولين',
          icon: Users,
          color: 'yellow'
        },
        {
          name: 'معتمد',
          description: 'تم الموافقة على المقترح وسيتم تحويله لمشروع',
          icon: CheckCircle,
          color: 'green'
        },
        {
          name: 'مرفوض',
          description: 'تم رفض المقترح مع ذكر الأسباب',
          icon: XCircle,
          color: 'red'
        }
      ]
    },
    {
      category: 'حالات المشروع',
      statuses: [
        {
          name: 'تخطيط',
          description: 'المشروع في مرحلة التخطيط وتحديد المتطلبات',
          icon: BookOpen,
          color: 'blue'
        },
        {
          name: 'قيد التنفيذ',
          description: 'المشروع قيد التنفيذ الفعلي',
          icon: Settings,
          color: 'green'
        },
        {
          name: 'معلق',
          description: 'المشروع متوقف مؤقتاً لأسباب معينة',
          icon: AlertCircle,
          color: 'yellow'
        },
        {
          name: 'مكتمل',
          description: 'تم إنجاز المشروع بنجاح',
          icon: Target,
          color: 'green'
        },
        {
          name: 'ملغي',
          description: 'تم إلغاء المشروع',
          icon: XCircle,
          color: 'red'
        }
      ]
    }
  ]

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'gray':
        return 'bg-gray-100 text-gray-600 border-gray-200'
      case 'blue':
        return 'bg-blue-100 text-blue-600 border-blue-200'
      case 'yellow':
        return 'bg-yellow-100 text-yellow-600 border-yellow-200'
      case 'green':
        return 'bg-green-100 text-green-600 border-green-200'
      case 'red':
        return 'bg-red-100 text-red-600 border-red-200'
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200'
    }
  }

  return (
    <ProtectedLayout>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/dashboard">
              <Button variant="secondary" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                العودة للوحة التحكم
              </Button>
            </Link>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            دليل سير العمل
          </h1>
          <p className="text-gray-600 text-lg">
            فهم شامل لسير العمل من إنشاء المقترح إلى تنفيذ المشروع
          </p>
        </div>

        {/* شرح سير العمل الرئيسي */}
        <WorkflowExplanation showDetailed={true} className="mb-8" />

        {/* شرح الحالات */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {statusExplanations.map((category, categoryIndex) => (
            <Card key={categoryIndex} className="p-6">
              <h3 className="text-xl font-semibold mb-4 text-gray-900">
                {category.category}
              </h3>
              
              <div className="space-y-3">
                {category.statuses.map((status, statusIndex) => (
                  <div 
                    key={statusIndex}
                    className={`
                      p-3 rounded-lg border transition-all duration-200 hover:shadow-sm
                      ${getColorClasses(status.color)}
                    `}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <status.icon className="h-4 w-4" />
                      <span className="font-medium">
                        {status.name}
                      </span>
                    </div>
                    <p className="text-sm opacity-80">
                      {status.description}
                    </p>
                  </div>
                ))}
              </div>
            </Card>
          ))}
        </div>

        {/* أدلة سريعة */}
        <Card className="p-6">
          <h3 className="text-xl font-semibold mb-4 text-gray-900">
            أدلة سريعة
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link href="/requests/new">
              <div className="p-4 border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors cursor-pointer">
                <FileText className="h-6 w-6 text-blue-600 mb-2" />
                <h4 className="font-medium text-gray-900 mb-1">
                  إنشاء مقترح جديد
                </h4>
                <p className="text-sm text-gray-600">
                  ابدأ بإنشاء مقترح مشروع جديد
                </p>
              </div>
            </Link>

            <Link href="/requests">
              <div className="p-4 border border-green-200 rounded-lg hover:bg-green-50 transition-colors cursor-pointer">
                <BookOpen className="h-6 w-6 text-green-600 mb-2" />
                <h4 className="font-medium text-gray-900 mb-1">
                  مقترحاتي
                </h4>
                <p className="text-sm text-gray-600">
                  عرض وإدارة مقترحاتي
                </p>
              </div>
            </Link>

            <Link href="/projects">
              <div className="p-4 border border-purple-200 rounded-lg hover:bg-purple-50 transition-colors cursor-pointer">
                <Target className="h-6 w-6 text-purple-600 mb-2" />
                <h4 className="font-medium text-gray-900 mb-1">
                  المشاريع
                </h4>
                <p className="text-sm text-gray-600">
                  متابعة المشاريع الجارية
                </p>
              </div>
            </Link>
          </div>
        </Card>

        {/* نصائح مهمة */}
        <Card className="p-6 bg-blue-50 border-blue-200 mt-6">
          <h3 className="text-lg font-semibold mb-3 text-blue-900">
            نصائح للاستخدام الأمثل
          </h3>
          
          <ul className="space-y-2 text-blue-800">
            <li className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
              <span className="text-sm">
                احرص على تعبئة جميع البيانات المطلوبة بدقة لتسريع عملية المراجعة
              </span>
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
              <span className="text-sm">
                استخدم خاصية حفظ المسودة للعمل على المقترح على عدة مراحل
              </span>
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
              <span className="text-sm">
                تابع حالة مقترحاتك بانتظام من خلال صفحة "مقترحاتي"
              </span>
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
              <span className="text-sm">
                في حالة رفض المقترح، راجع الملاحظات وقم بتحسين المقترح وإعادة إرساله
              </span>
            </li>
          </ul>
        </Card>
      </div>
    </ProtectedLayout>
  )
}
