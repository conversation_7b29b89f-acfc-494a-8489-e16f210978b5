'use client'

import React from 'react'
import { AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react'

// أنواع رسائل التحقق
export type ValidationMessageType = 'error' | 'warning' | 'success' | 'info'

export interface ValidationMessage {
  type: ValidationMessageType
  message: string
  field?: string
  suggestion?: string
}

interface ValidationDisplayProps {
  messages: ValidationMessage[]
  className?: string
}

export function ValidationDisplay({ messages, className = '' }: ValidationDisplayProps) {
  if (messages.length === 0) return null

  const getIcon = (type: ValidationMessageType) => {
    switch (type) {
      case 'error':
        return <AlertCircle className="w-4 h-4" />
      case 'warning':
        return <AlertTriangle className="w-4 h-4" />
      case 'success':
        return <CheckCircle className="w-4 h-4" />
      case 'info':
        return <Info className="w-4 h-4" />
    }
  }

  const getClasses = (type: ValidationMessageType) => {
    switch (type) {
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800'
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800'
    }
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {messages.map((msg, index) => (
        <div
          key={index}
          className={`p-3 rounded-lg border flex items-start gap-2 ${getClasses(msg.type)}`}
        >
          {getIcon(msg.type)}
          <div className="flex-1">
            <p className="text-sm font-medium">{msg.message}</p>
            {msg.suggestion && (
              <p className="text-xs mt-1 opacity-80">{msg.suggestion}</p>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}

// مكون لعرض رسالة خطأ واحدة
interface FieldErrorProps {
  error?: string
  warning?: string
  success?: string
  className?: string
}

export function FieldError({ error, warning, success, className = '' }: FieldErrorProps) {
  if (!error && !warning && !success) return null

  const message = error || warning || success
  const type: ValidationMessageType = error ? 'error' : warning ? 'warning' : 'success'

  return (
    <ValidationDisplay 
      messages={[{ type, message: message! }]} 
      className={className}
    />
  )
}

// فئة للتحقق من صحة البيانات المحسنة
export class EnhancedValidator {
  private errors: Record<string, ValidationMessage[]> = {}

  // إضافة رسالة تحقق
  addMessage(field: string, message: ValidationMessage) {
    if (!this.errors[field]) {
      this.errors[field] = []
    }
    this.errors[field].push(message)
  }

  // إضافة خطأ
  addError(field: string, message: string, suggestion?: string) {
    this.addMessage(field, { type: 'error', message, field, suggestion })
  }

  // إضافة تحذير
  addWarning(field: string, message: string, suggestion?: string) {
    this.addMessage(field, { type: 'warning', message, field, suggestion })
  }

  // إضافة نجاح
  addSuccess(field: string, message: string) {
    this.addMessage(field, { type: 'success', message, field })
  }

  // إضافة معلومة
  addInfo(field: string, message: string) {
    this.addMessage(field, { type: 'info', message, field })
  }

  // مسح رسائل حقل معين
  clearField(field: string) {
    delete this.errors[field]
  }

  // مسح جميع الرسائل
  clearAll() {
    this.errors = {}
  }

  // الحصول على رسائل حقل معين
  getFieldMessages(field: string): ValidationMessage[] {
    return this.errors[field] || []
  }

  // الحصول على جميع الرسائل
  getAllMessages(): Record<string, ValidationMessage[]> {
    return this.errors
  }

  // التحقق من وجود أخطاء
  hasErrors(): boolean {
    return Object.values(this.errors).some(messages => 
      messages.some(msg => msg.type === 'error')
    )
  }

  // الحصول على عدد الأخطاء
  getErrorCount(): number {
    return Object.values(this.errors).reduce((count, messages) => 
      count + messages.filter(msg => msg.type === 'error').length, 0
    )
  }

  // التحقق من صحة النص
  validateText(field: string, value: string, options: {
    required?: boolean
    minLength?: number
    maxLength?: number
    pattern?: RegExp
    customMessage?: string
  } = {}) {
    this.clearField(field)

    if (options.required && !value?.trim()) {
      this.addError(field, options.customMessage || 'هذا الحقل مطلوب')
      return false
    }

    if (value && options.minLength && value.length < options.minLength) {
      this.addError(field, `يجب أن يكون النص ${options.minLength} أحرف على الأقل`)
      return false
    }

    if (value && options.maxLength && value.length > options.maxLength) {
      this.addError(field, `يجب أن لا يتجاوز النص ${options.maxLength} حرف`)
      return false
    }

    if (value && options.pattern && !options.pattern.test(value)) {
      this.addError(field, options.customMessage || 'تنسيق النص غير صحيح')
      return false
    }

    if (value?.trim()) {
      this.addSuccess(field, 'تم التحقق بنجاح')
    }

    return true
  }

  // التحقق من صحة الرقم
  validateNumber(field: string, value: number, options: {
    required?: boolean
    min?: number
    max?: number
    integer?: boolean
    customMessage?: string
  } = {}) {
    this.clearField(field)

    if (options.required && (value === undefined || value === null)) {
      this.addError(field, options.customMessage || 'هذا الحقل مطلوب')
      return false
    }

    if (value !== undefined && value !== null) {
      if (options.min !== undefined && value < options.min) {
        this.addError(field, `يجب أن تكون القيمة ${options.min} أو أكثر`)
        return false
      }

      if (options.max !== undefined && value > options.max) {
        this.addError(field, `يجب أن تكون القيمة ${options.max} أو أقل`)
        return false
      }

      if (options.integer && !Number.isInteger(value)) {
        this.addError(field, 'يجب أن تكون القيمة رقم صحيح')
        return false
      }

      this.addSuccess(field, 'تم التحقق بنجاح')
    }

    return true
  }

  // التحقق من صحة البريد الإلكتروني
  validateEmail(field: string, value: string, required: boolean = false) {
    this.clearField(field)

    if (required && !value?.trim()) {
      this.addError(field, 'البريد الإلكتروني مطلوب')
      return false
    }

    if (value?.trim()) {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailPattern.test(value)) {
        this.addError(field, 'تنسيق البريد الإلكتروني غير صحيح', 'مثال: <EMAIL>')
        return false
      }
      this.addSuccess(field, 'البريد الإلكتروني صحيح')
    }

    return true
  }

  // التحقق من صحة رقم الهاتف السعودي
  validateSaudiPhone(field: string, value: string, required: boolean = false) {
    this.clearField(field)

    if (required && !value?.trim()) {
      this.addError(field, 'رقم الهاتف مطلوب')
      return false
    }

    if (value?.trim()) {
      const phonePattern = /^05\d{8}$/
      if (!phonePattern.test(value)) {
        this.addError(field, 'رقم الهاتف غير صحيح', 'يجب أن يبدأ بـ 05 ويكون 10 أرقام')
        return false
      }
      this.addSuccess(field, 'رقم الهاتف صحيح')
    }

    return true
  }

  // التحقق من صحة التاريخ
  validateDate(field: string, value: string, options: {
    required?: boolean
    minDate?: string
    maxDate?: string
    customMessage?: string
  } = {}) {
    this.clearField(field)

    if (options.required && !value) {
      this.addError(field, options.customMessage || 'التاريخ مطلوب')
      return false
    }

    if (value) {
      const date = new Date(value)
      if (isNaN(date.getTime())) {
        this.addError(field, 'تنسيق التاريخ غير صحيح')
        return false
      }

      if (options.minDate && date < new Date(options.minDate)) {
        this.addError(field, `التاريخ يجب أن يكون بعد ${options.minDate}`)
        return false
      }

      if (options.maxDate && date > new Date(options.maxDate)) {
        this.addError(field, `التاريخ يجب أن يكون قبل ${options.maxDate}`)
        return false
      }

      this.addSuccess(field, 'التاريخ صحيح')
    }

    return true
  }

  // التحقق من صحة المصفوفة
  validateArray(field: string, value: any[], options: {
    required?: boolean
    minLength?: number
    maxLength?: number
    customMessage?: string
  } = {}) {
    this.clearField(field)

    if (options.required && (!value || value.length === 0)) {
      this.addError(field, options.customMessage || 'يجب إضافة عنصر واحد على الأقل')
      return false
    }

    if (value && value.length > 0) {
      if (options.minLength && value.length < options.minLength) {
        this.addError(field, `يجب إضافة ${options.minLength} عناصر على الأقل`)
        return false
      }

      if (options.maxLength && value.length > options.maxLength) {
        this.addError(field, `لا يمكن إضافة أكثر من ${options.maxLength} عناصر`)
        return false
      }

      this.addSuccess(field, `تم إضافة ${value.length} عنصر`)
    }

    return true
  }
}

// Hook لاستخدام التحقق المحسن
export function useEnhancedValidation() {
  const [validator] = React.useState(() => new EnhancedValidator())
  const [messages, setMessages] = React.useState<Record<string, ValidationMessage[]>>({})

  const updateMessages = () => {
    setMessages({ ...validator.getAllMessages() })
  }

  const validate = React.useCallback((field: string, value: any, rules: any) => {
    // تنفيذ قواعد التحقق
    if (rules.text) {
      validator.validateText(field, value, rules.text)
    }
    if (rules.number) {
      validator.validateNumber(field, value, rules.number)
    }
    if (rules.email) {
      validator.validateEmail(field, value, rules.email.required)
    }
    if (rules.phone) {
      validator.validateSaudiPhone(field, value, rules.phone.required)
    }
    if (rules.date) {
      validator.validateDate(field, value, rules.date)
    }
    if (rules.array) {
      validator.validateArray(field, value, rules.array)
    }

    updateMessages()
    return !validator.hasErrors()
  }, [validator])

  const clearField = React.useCallback((field: string) => {
    validator.clearField(field)
    updateMessages()
  }, [validator])

  const clearAll = React.useCallback(() => {
    validator.clearAll()
    updateMessages()
  }, [validator])

  const hasErrors = React.useMemo(() => validator.hasErrors(), [messages])
  const errorCount = React.useMemo(() => validator.getErrorCount(), [messages])
  const getFieldMessages = React.useCallback((field: string) => validator.getFieldMessages(field), [validator])

  return {
    validate,
    clearField,
    clearAll,
    messages,
    hasErrors,
    errorCount,
    getFieldMessages
  }
}
