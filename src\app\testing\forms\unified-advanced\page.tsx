'use client'

import React, { useState } from 'react'
import { useAuth } from '@/lib/core/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { UnifiedProjectForm, FormType } from '@/components/forms/unified/UnifiedProjectForm'
import Link from 'next/link'
import { 
  ArrowLeft, 
  Play, 
  CheckCircle, 
  AlertCircle, 
  Save,
  Send,
  Target,
  Clock,
  Database
} from 'lucide-react'

export default function UnifiedAdvancedTestPage() {
  const [selectedType, setSelectedType] = useState<FormType | null>(null)
  const [testResults, setTestResults] = useState<{[key: string]: any}>({})
  const [isLoading, setIsLoading] = useState(false)
  const [testPhase, setTestPhase] = useState<'idle' | 'validation' | 'draft' | 'submission'>('idle')
  
  const { user } = useAuth()

  const formTypes = [
    {
      id: 'enhanced_improvement' as FormType,
      title: 'تحسين شامل',
      description: 'للمشاريع الكبيرة والتحسينات الشاملة',
      color: 'bg-blue-50 border-blue-200',
      icon: '🎯'
    },
    {
      id: 'suggestion' as FormType,
      title: 'اقتراح تحسين',
      description: 'للاقتراحات والأفكار التحسينية',
      color: 'bg-green-50 border-green-200',
      icon: '💡'
    },
    {
      id: 'quick_win' as FormType,
      title: 'كويك وين',
      description: 'للتحسينات السريعة (حد أقصى 4 أسابيع)',
      color: 'bg-orange-50 border-orange-200',
      icon: '⚡'
    }
  ]

  const handleSubmit = async (data: any) => {
    setTestPhase('submission')
    setIsLoading(true)
    
    try {
      const requestData = {
        request_number: `TEST-${selectedType?.toUpperCase()}-${Date.now()}`,
        form_type: selectedType,
        form_data: data,
        status: 'submitted',
        created_by: user?.id || 'test-user-id',
        created_at: new Date().toISOString(),
      }

      const response = await fetch('/api/requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'فشل في إرسال النموذج')
      }

      setTestResults(prev => ({
        ...prev,
        submission: {
          success: true,
          message: 'تم إرسال النموذج بنجاح',
          data: result,
          timestamp: new Date().toISOString()
        }
      }))

      alert(`تم إرسال النموذج بنجاح! رقم الطلب: ${result.request_number}`)

    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        submission: {
          success: false,
          message: error instanceof Error ? error.message : 'خطأ غير معروف',
          timestamp: new Date().toISOString()
        }
      }))
      
      alert(`خطأ في الإرسال: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    } finally {
      setIsLoading(false)
      setTestPhase('idle')
    }
  }

  const handleSaveDraft = async (data: any) => {
    setTestPhase('draft')
    
    try {
      if (!user?.id) {
        throw new Error('يجب تسجيل الدخول لحفظ المسودة')
      }

      const draftData = {
        form_type: selectedType,
        form_data: data,
        user_id: user.id
      }

      const response = await fetch('/api/requests/drafts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(draftData)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'فشل في حفظ المسودة')
      }

      setTestResults(prev => ({
        ...prev,
        draft: {
          success: true,
          message: 'تم حفظ المسودة بنجاح',
          data: result,
          timestamp: new Date().toISOString()
        }
      }))

      alert('تم حفظ المسودة بنجاح!')

    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        draft: {
          success: false,
          message: error instanceof Error ? error.message : 'خطأ غير معروف',
          timestamp: new Date().toISOString()
        }
      }))
      
      alert(`خطأ في حفظ المسودة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    } finally {
      setTestPhase('idle')
    }
  }

  const resetTest = () => {
    setSelectedType(null)
    setTestResults({})
    setTestPhase('idle')
  }

  if (selectedType) {
    return (
      <ProtectedLayout>
        <div className="min-h-screen bg-gray-50 p-6">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="flex items-center gap-4 mb-6">
              <Button
                onClick={resetTest}
                variant="secondary"
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                العودة للاختيار
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  اختبار متقدم: {formTypes.find(t => t.id === selectedType)?.title}
                </h1>
                <p className="text-gray-600">
                  اختبار شامل للنموذج مع جميع الميزات المحدثة
                </p>
              </div>
            </div>

            {/* Test Status */}
            {testPhase !== 'idle' && (
              <Card className="mb-6 p-4 bg-yellow-50 border-yellow-200">
                <div className="flex items-center gap-3">
                  <Clock className="w-5 h-5 text-yellow-600" />
                  <div>
                    <p className="font-medium text-gray-900">
                      جاري تنفيذ الاختبار...
                    </p>
                    <p className="text-sm text-gray-600">
                      المرحلة: {
                        testPhase === 'validation' ? 'التحقق من البيانات' :
                        testPhase === 'draft' ? 'حفظ المسودة' :
                        testPhase === 'submission' ? 'إرسال النموذج' : 'جاري التحضير'
                      }
                    </p>
                  </div>
                </div>
              </Card>
            )}

            {/* Test Results */}
            {Object.keys(testResults).length > 0 && (
              <Card className="mb-6 p-6">
                <h3 className="text-lg font-semibold mb-4">نتائج الاختبارات</h3>
                <div className="space-y-3">
                  {Object.entries(testResults).map(([key, result]) => (
                    <div key={key} className="flex items-center gap-3 p-3 rounded-lg bg-gray-50">
                      {result.success ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <AlertCircle className="w-5 h-5 text-red-500" />
                      )}
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">
                          {key === 'submission' ? 'إرسال النموذج' :
                           key === 'draft' ? 'حفظ المسودة' :
                           key === 'validation' ? 'التحقق من البيانات' : key}
                        </p>
                        <p className={`text-sm ${result.success ? 'text-green-600' : 'text-red-600'}`}>
                          {result.message}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(result.timestamp).toLocaleString('ar-SA')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* Form */}
            <UnifiedProjectForm
              formType={selectedType}
              onSubmit={handleSubmit}
              onSaveDraft={handleSaveDraft}
              isLoading={isLoading}
            />
          </div>
        </div>
      </ProtectedLayout>
    )
  }

  return (
    <ProtectedLayout>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          {/* Navigation */}
          <div className="mb-6">
            <Link 
              href="/testing/forms" 
              className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              العودة إلى اختبارات النماذج
            </Link>
          </div>

          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              اختبار النموذج الموحد المتقدم
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              اختبار شامل للنموذج الموحد مع جميع التحسينات الجديدة: التحقق من البيانات، حفظ المسودات، والإرسال
            </p>
          </div>

          {/* Form Type Selection */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {formTypes.map((type) => (
              <Card 
                key={type.id} 
                className={`p-6 cursor-pointer hover:shadow-lg transition-all duration-200 ${type.color}`}
                onClick={() => setSelectedType(type.id)}
              >
                <div className="text-center">
                  <div className="text-4xl mb-4">{type.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {type.title}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {type.description}
                  </p>
                  <Button className="w-full">
                    <Play className="w-4 h-4 mr-2" />
                    اختبار هذا النموذج
                  </Button>
                </div>
              </Card>
            ))}
          </div>

          {/* Features */}
          <Card className="mt-8 p-6 bg-blue-50 border-blue-200">
            <h3 className="text-lg font-semibold text-blue-900 mb-4">
              الميزات المحدثة في هذا الاختبار:
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Target className="w-5 h-5 text-blue-600" />
                <span className="text-blue-800">التحقق الشامل من صحة البيانات</span>
              </div>
              <div className="flex items-center gap-2">
                <Save className="w-5 h-5 text-blue-600" />
                <span className="text-blue-800">حفظ المسودات التلقائي والمحسن</span>
              </div>
              <div className="flex items-center gap-2">
                <Send className="w-5 h-5 text-blue-600" />
                <span className="text-blue-800">إرسال محسن مع معالجة الأخطاء</span>
              </div>
              <div className="flex items-center gap-2">
                <Database className="w-5 h-5 text-blue-600" />
                <span className="text-blue-800">تتبع شامل لجميع العمليات</span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </ProtectedLayout>
  )
}
