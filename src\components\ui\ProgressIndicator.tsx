'use client'

import React from 'react'
import { CheckCircle, Circle, ArrowRight } from 'lucide-react'

interface Step {
  number: number
  title: string
  description: string
  icon?: React.ComponentType<any>
  isOptional?: boolean
}

interface ProgressIndicatorProps {
  steps: Step[]
  currentStep: number
  onStepClick?: (step: number) => void
  variant?: 'horizontal' | 'vertical'
  showDescription?: boolean
  allowNavigation?: boolean
  className?: string
}

export function ProgressIndicator({
  steps,
  currentStep,
  onStepClick,
  variant = 'horizontal',
  showDescription = true,
  allowNavigation = false,
  className = ''
}: ProgressIndicatorProps) {
  const getStepStatus = (stepNumber: number) => {
    if (stepNumber < currentStep) return 'completed'
    if (stepNumber === currentStep) return 'current'
    return 'upcoming'
  }

  const getStepClasses = (status: string) => {
    const baseClasses = 'transition-all duration-200'
    
    switch (status) {
      case 'completed':
        return `${baseClasses} bg-green-600 text-white border-green-600`
      case 'current':
        return `${baseClasses} bg-blue-600 text-white border-blue-600 ring-4 ring-blue-100`
      case 'upcoming':
        return `${baseClasses} bg-gray-200 text-gray-500 border-gray-200`
      default:
        return baseClasses
    }
  }

  const getConnectorClasses = (fromStep: number) => {
    const isCompleted = fromStep < currentStep
    return `transition-all duration-200 ${
      isCompleted ? 'bg-green-600' : 'bg-gray-200'
    }`
  }

  const handleStepClick = (stepNumber: number) => {
    if (allowNavigation && onStepClick && stepNumber <= currentStep) {
      onStepClick(stepNumber)
    }
  }

  if (variant === 'vertical') {
    return (
      <div className={`space-y-4 ${className}`}>
        {steps.map((step, index) => {
          const status = getStepStatus(step.number)
          const Icon = step.icon
          const isClickable = allowNavigation && step.number <= currentStep

          return (
            <div key={step.number} className="relative">
              <div
                className={`flex items-start gap-4 ${
                  isClickable ? 'cursor-pointer' : ''
                }`}
                onClick={() => handleStepClick(step.number)}
              >
                {/* Step Circle */}
                <div className="relative flex-shrink-0">
                  <div
                    className={`
                      w-10 h-10 rounded-full border-2 flex items-center justify-center font-semibold text-sm
                      ${getStepClasses(status)}
                      ${isClickable ? 'hover:scale-105' : ''}
                    `}
                  >
                    {status === 'completed' ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : Icon ? (
                      <Icon className="w-5 h-5" />
                    ) : (
                      step.number
                    )}
                  </div>
                  
                  {/* Connector Line */}
                  {index < steps.length - 1 && (
                    <div
                      className={`
                        absolute top-10 left-1/2 transform -translate-x-1/2 w-0.5 h-8
                        ${getConnectorClasses(step.number)}
                      `}
                    />
                  )}
                </div>

                {/* Step Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <h3
                      className={`font-medium ${
                        status === 'current'
                          ? 'text-blue-900'
                          : status === 'completed'
                          ? 'text-green-900'
                          : 'text-gray-500'
                      }`}
                    >
                      {step.title}
                    </h3>
                    {step.isOptional && (
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                        اختياري
                      </span>
                    )}
                  </div>
                  {showDescription && (
                    <p
                      className={`text-sm mt-1 ${
                        status === 'current'
                          ? 'text-blue-700'
                          : status === 'completed'
                          ? 'text-green-700'
                          : 'text-gray-400'
                      }`}
                    >
                      {step.description}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  // Horizontal variant
  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const status = getStepStatus(step.number)
          const Icon = step.icon
          const isClickable = allowNavigation && step.number <= currentStep

          return (
            <React.Fragment key={step.number}>
              <div
                className={`flex flex-col items-center ${
                  isClickable ? 'cursor-pointer' : ''
                }`}
                onClick={() => handleStepClick(step.number)}
              >
                {/* Step Circle */}
                <div
                  className={`
                    w-12 h-12 rounded-full border-2 flex items-center justify-center font-semibold mb-2
                    ${getStepClasses(status)}
                    ${isClickable ? 'hover:scale-105' : ''}
                  `}
                >
                  {status === 'completed' ? (
                    <CheckCircle className="w-6 h-6" />
                  ) : Icon ? (
                    <Icon className="w-6 h-6" />
                  ) : (
                    step.number
                  )}
                </div>

                {/* Step Content */}
                <div className="text-center max-w-24">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <p
                      className={`text-sm font-medium ${
                        status === 'current'
                          ? 'text-blue-900'
                          : status === 'completed'
                          ? 'text-green-900'
                          : 'text-gray-500'
                      }`}
                    >
                      {step.title}
                    </p>
                    {step.isOptional && (
                      <span className="text-xs bg-gray-100 text-gray-600 px-1 py-0.5 rounded">
                        *
                      </span>
                    )}
                  </div>
                  {showDescription && (
                    <p
                      className={`text-xs ${
                        status === 'current'
                          ? 'text-blue-700'
                          : status === 'completed'
                          ? 'text-green-700'
                          : 'text-gray-400'
                      }`}
                    >
                      {step.description}
                    </p>
                  )}
                </div>
              </div>

              {/* Connector */}
              {index < steps.length - 1 && (
                <div className="flex-1 flex items-center justify-center">
                  <div
                    className={`
                      h-0.5 flex-1 max-w-24
                      ${getConnectorClasses(step.number)}
                    `}
                  />
                </div>
              )}
            </React.Fragment>
          )
        })}
      </div>

      {/* Progress Bar */}
      <div className="mt-6">
        <div className="flex justify-between text-xs text-gray-500 mb-2">
          <span>التقدم</span>
          <span>{Math.round(((currentStep - 1) / (steps.length - 1)) * 100)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-600 to-green-600 h-2 rounded-full transition-all duration-500"
            style={{
              width: `${((currentStep - 1) / (steps.length - 1)) * 100}%`
            }}
          />
        </div>
      </div>
    </div>
  )
}

// Preset configurations for common use cases
export const ProjectSteps: Step[] = [
  {
    number: 1,
    title: 'معلومات أساسية',
    description: 'تفاصيل المشروع الأساسية'
  },
  {
    number: 2,
    title: 'المشكلة والهدف',
    description: 'تحديد المشكلة والهدف'
  },
  {
    number: 3,
    title: 'الحل والمهام',
    description: 'تخطيط الحل والمهام'
  },
  {
    number: 4,
    title: 'الفريق والموارد',
    description: 'تحديد الفريق والموارد'
  },
  {
    number: 5,
    title: 'المراجعة والإرسال',
    description: 'مراجعة نهائية وإرسال'
  }
]

export const ProposalSteps: Step[] = [
  {
    number: 1,
    title: 'معلومات أساسية',
    description: 'تفاصيل المقترح الأساسية'
  },
  {
    number: 2,
    title: 'وصف المشكلة',
    description: 'تحديد المشكلة أو الفرصة'
  },
  {
    number: 3,
    title: 'الحل المقترح',
    description: 'اقتراح الحلول والتوصيات'
  },
  {
    number: 4,
    title: 'المراجعة والإرسال',
    description: 'مراجعة نهائية وإرسال'
  }
]
