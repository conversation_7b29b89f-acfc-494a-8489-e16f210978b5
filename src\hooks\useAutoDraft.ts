import { useEffect, useRef, useCallback } from 'react'
import { FormType } from '@/components/forms/unified/UnifiedProjectForm'

interface UseAutoDraftOptions {
  formData: any
  formType: FormType
  userId: string | null
  onSaveDraft: (data: any) => Promise<void>
  autoSaveInterval?: number // بالثواني، افتراضي 30 ثانية
  enabled?: boolean
}

export function useAutoDraft({
  formData,
  formType,
  userId,
  onSaveDraft,
  autoSaveInterval = 30,
  enabled = true
}: UseAutoDraftOptions) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastSavedDataRef = useRef<string>('')
  const isInitialRender = useRef(true)

  // دالة التحقق من وجود بيانات كافية للحفظ
  const hasMinimumData = useCallback((data: any): boolean => {
    switch (formType) {
      case 'enhanced_improvement':
        return !!(data.projectName?.trim() || data.projectDescription?.trim())
      case 'quick_win':
        return !!(data.projectTitle?.trim() || data.section?.trim())
      case 'suggestion':
        return !!(data.problemDescription?.trim())
      default:
        return false
    }
  }, [formType])

  // دالة الحفظ التلقائي
  const autoSave = useCallback(async () => {
    if (!enabled || !userId || !hasMinimumData(formData)) {
      return
    }

    const currentDataString = JSON.stringify(formData)
    
    // تجنب الحفظ إذا لم تتغير البيانات
    if (currentDataString === lastSavedDataRef.current) {
      return
    }

    try {
      await onSaveDraft(formData)
      lastSavedDataRef.current = currentDataString
      console.log('تم حفظ المسودة تلقائياً')
    } catch (error) {
      console.error('خطأ في الحفظ التلقائي:', error)
    }
  }, [enabled, userId, formData, hasMinimumData, onSaveDraft])

  // إعداد الحفظ التلقائي
  useEffect(() => {
    if (!enabled || !userId) {
      return
    }

    // تجنب الحفظ في أول تحميل
    if (isInitialRender.current) {
      isInitialRender.current = false
      lastSavedDataRef.current = JSON.stringify(formData)
      return
    }

    // مسح المؤقت السابق
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    // إعداد مؤقت جديد
    intervalRef.current = setInterval(autoSave, autoSaveInterval * 1000)

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [autoSave, autoSaveInterval, enabled, userId])

  // حفظ عند تغيير البيانات (مع تأخير)
  useEffect(() => {
    if (!enabled || !userId || isInitialRender.current) {
      return
    }

    const timeoutId = setTimeout(() => {
      if (hasMinimumData(formData)) {
        autoSave()
      }
    }, 2000) // تأخير 2 ثانية بعد آخر تغيير

    return () => clearTimeout(timeoutId)
  }, [formData, autoSave, enabled, userId, hasMinimumData])

  // تنظيف عند إلغاء التحميل
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  // دالة الحفظ اليدوي
  const saveNow = useCallback(async () => {
    if (!userId || !hasMinimumData(formData)) {
      throw new Error('لا توجد بيانات كافية للحفظ')
    }

    try {
      await onSaveDraft(formData)
      lastSavedDataRef.current = JSON.stringify(formData)
      return true
    } catch (error) {
      console.error('خطأ في الحفظ اليدوي:', error)
      throw error
    }
  }, [userId, formData, hasMinimumData, onSaveDraft])

  return {
    saveNow,
    hasMinimumData: hasMinimumData(formData),
    isAutoSaveEnabled: enabled && !!userId
  }
}
