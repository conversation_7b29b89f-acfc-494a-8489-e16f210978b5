import { useEffect, useRef, useCallback, useMemo } from 'react'
import { FormType } from '@/components/forms/unified/UnifiedProjectForm'

interface UseAutoDraftOptions {
  formData: any
  formType: FormType
  userId: string | null
  onSaveDraft: (data: any) => Promise<void>
  autoSaveInterval?: number // بالثواني، افتراضي 30 ثانية
  enabled?: boolean
}

export function useAutoDraft({
  formData,
  formType,
  userId,
  onSaveDraft,
  autoSaveInterval = 30,
  enabled = true
}: UseAutoDraftOptions) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastSavedDataRef = useRef<string>('')
  const isInitialRender = useRef(true)
  const formDataRef = useRef(formData)

  // تحديث ref عند تغيير البيانات
  useEffect(() => {
    formDataRef.current = formData
  }, [formData])

  // دالة التحقق من وجود بيانات كافية للحفظ
  const hasMinimumData = useCallback((data: any): boolean => {
    switch (formType) {
      case 'enhanced_improvement':
        return !!(data.projectName?.trim() || data.projectDescription?.trim())
      case 'quick_win':
        return !!(data.projectTitle?.trim() || data.section?.trim())
      case 'suggestion':
        return !!(data.problemDescription?.trim())
      default:
        return false
    }
  }, [formType])

  // دالة الحفظ التلقائي
  const autoSave = useCallback(async () => {
    if (!enabled || !userId) {
      return
    }

    const currentData = formDataRef.current
    const currentDataString = JSON.stringify(currentData)

    // تجنب الحفظ إذا لم تتغير البيانات
    if (currentDataString === lastSavedDataRef.current) {
      return
    }

    // التحقق من وجود بيانات كافية
    if (!hasMinimumData(currentData)) {
      return
    }

    try {
      await onSaveDraft(currentData)
      lastSavedDataRef.current = currentDataString
      console.log('تم حفظ المسودة تلقائياً')
    } catch (error) {
      console.error('خطأ في الحفظ التلقائي:', error)
    }
  }, [enabled, userId, onSaveDraft])

  // إعداد الحفظ التلقائي
  useEffect(() => {
    if (!enabled || !userId) {
      return
    }

    // تجنب الحفظ في أول تحميل
    if (isInitialRender.current) {
      isInitialRender.current = false
      lastSavedDataRef.current = JSON.stringify(formDataRef.current)
      return
    }

    // مسح المؤقت السابق
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    // إعداد مؤقت جديد
    intervalRef.current = setInterval(() => {
      autoSave()
    }, autoSaveInterval * 1000)

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [autoSaveInterval, enabled, userId]) // إزالة autoSave من dependencies

  // حفظ عند تغيير البيانات (مع تأخير)
  useEffect(() => {
    if (!enabled || !userId || isInitialRender.current) {
      return
    }

    const timeoutId = setTimeout(() => {
      if (hasMinimumData(formDataRef.current)) {
        autoSave()
      }
    }, 2000) // تأخير 2 ثانية بعد آخر تغيير

    return () => clearTimeout(timeoutId)
  }, [formData, enabled, userId]) // إزالة autoSave و hasMinimumData من dependencies

  // تنظيف عند إلغاء التحميل
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  // دالة الحفظ اليدوي
  const saveNow = useCallback(async () => {
    const currentData = formDataRef.current
    if (!userId || !hasMinimumData(currentData)) {
      throw new Error('لا توجد بيانات كافية للحفظ')
    }

    try {
      await onSaveDraft(currentData)
      lastSavedDataRef.current = JSON.stringify(currentData)
      return true
    } catch (error) {
      console.error('خطأ في الحفظ اليدوي:', error)
      throw error
    }
  }, [userId, onSaveDraft])

  return {
    saveNow,
    hasMinimumData: hasMinimumData(formDataRef.current),
    isAutoSaveEnabled: enabled && !!userId
  }
}
