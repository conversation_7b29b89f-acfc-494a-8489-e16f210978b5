'use client'

import React, { useState } from 'react'
import { useAuth } from '@/lib/core/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'
import { 
  ArrowLeft, 
  Play, 
  Clock, 
  Database,
  Zap,
  BarChart3,
  CheckCircle,
  AlertCircle,
  TrendingUp
} from 'lucide-react'

interface PerformanceResult {
  operation: string
  duration: number
  success: boolean
  error?: string
  timestamp: string
}

export default function PerformanceTestPage() {
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<PerformanceResult[]>([])
  const [summary, setSummary] = useState<{
    totalTests: number
    successRate: number
    averageTime: number
    fastestTime: number
    slowestTime: number
  } | null>(null)
  
  const { user } = useAuth()

  const generateTestData = (formType: string) => {
    const baseData = {
      form_type: formType,
      created_by: user?.id || 'test-user-id',
      created_at: new Date().toISOString()
    }

    switch (formType) {
      case 'quick_win':
        return {
          ...baseData,
          form_data: {
            projectTitle: `اختبار الأداء - كويك وين ${Date.now()}`,
            section: 'قسم الاختبارات',
            projectExecutor: {
              name: 'مختبر النظام',
              phone: '0501234567',
              email: '<EMAIL>'
            },
            problemDescription: 'وصف مشكلة اختبار الأداء للتأكد من سرعة النظام وكفاءته',
            solution: {
              description: 'حل اختبار الأداء لقياس سرعة الاستجابة'
            },
            indicatorName: 'مؤشر الأداء',
            currentValue: 50,
            targetValue: 80,
            unit: 'نسبة مئوية',
            improvementDirection: 'increase'
          }
        }

      case 'suggestion':
        return {
          ...baseData,
          form_data: {
            problemDescription: `اختبار أداء الاقتراحات - ${Date.now()}`,
            indicatorName: 'مؤشر الاقتراح',
            currentValue: 30,
            targetValue: 70,
            unit: 'درجة',
            improvementDirection: 'increase',
            responsibleDepartment: 'قسم التطوير',
            teamLeader: {
              name: 'قائد فريق الاختبار',
              phone: '0507654321',
              email: '<EMAIL>'
            }
          }
        }

      default:
        return baseData
    }
  }

  const measureTime = async (operation: string, asyncFunction: () => Promise<any>) => {
    const startTime = performance.now()
    
    try {
      const result = await asyncFunction()
      const endTime = performance.now()
      const duration = endTime - startTime

      const performanceResult: PerformanceResult = {
        operation,
        duration,
        success: true,
        timestamp: new Date().toISOString()
      }

      setResults(prev => [...prev, performanceResult])
      return { success: true, duration, result }

    } catch (error) {
      const endTime = performance.now()
      const duration = endTime - startTime

      const performanceResult: PerformanceResult = {
        operation,
        duration,
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        timestamp: new Date().toISOString()
      }

      setResults(prev => [...prev, performanceResult])
      return { success: false, duration, error }
    }
  }

  const runPerformanceTests = async () => {
    setIsRunning(true)
    setResults([])
    setSummary(null)

    const testOperations = [
      {
        name: 'إرسال نموذج كويك وين',
        operation: async () => {
          const response = await fetch('/api/requests', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(generateTestData('quick_win'))
          })
          return response.json()
        }
      },
      {
        name: 'حفظ مسودة كويك وين',
        operation: async () => {
          const response = await fetch('/api/requests/drafts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              form_type: 'quick_win',
              form_data: generateTestData('quick_win').form_data,
              user_id: user?.id || 'test-user-id'
            })
          })
          return response.json()
        }
      },
      {
        name: 'إرسال نموذج اقتراح',
        operation: async () => {
          const response = await fetch('/api/requests', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(generateTestData('suggestion'))
          })
          return response.json()
        }
      },
      {
        name: 'حفظ مسودة اقتراح',
        operation: async () => {
          const response = await fetch('/api/requests/drafts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              form_type: 'suggestion',
              form_data: generateTestData('suggestion').form_data,
              user_id: user?.id || 'test-user-id'
            })
          })
          return response.json()
        }
      },
      {
        name: 'جلب قائمة الطلبات',
        operation: async () => {
          const response = await fetch('/api/requests')
          return response.json()
        }
      }
    ]

    // تشغيل الاختبارات
    for (const test of testOperations) {
      await measureTime(test.name, test.operation)
      // تأخير قصير بين الاختبارات
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // حساب الإحصائيات
    const allResults = results.concat() // نسخة من النتائج
    const successfulResults = allResults.filter(r => r.success)
    const durations = allResults.map(r => r.duration)

    setSummary({
      totalTests: allResults.length,
      successRate: (successfulResults.length / allResults.length) * 100,
      averageTime: durations.reduce((a, b) => a + b, 0) / durations.length,
      fastestTime: Math.min(...durations),
      slowestTime: Math.max(...durations)
    })

    setIsRunning(false)
  }

  const formatTime = (ms: number) => {
    if (ms < 1000) {
      return `${ms.toFixed(0)} مللي ثانية`
    }
    return `${(ms / 1000).toFixed(2)} ثانية`
  }

  const getPerformanceColor = (duration: number) => {
    if (duration < 500) return 'text-green-600'
    if (duration < 1000) return 'text-yellow-600'
    if (duration < 2000) return 'text-orange-600'
    return 'text-red-600'
  }

  return (
    <ProtectedLayout>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          {/* Navigation */}
          <div className="mb-6">
            <Link 
              href="/testing/forms" 
              className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              العودة إلى اختبارات النماذج
            </Link>
          </div>

          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              اختبار الأداء
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              قياس أداء النظام وسرعة الاستجابة لعمليات النماذج والمسودات
            </p>
          </div>

          {/* Test Controls */}
          <Card className="mb-6 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  تشغيل اختبارات الأداء
                </h3>
                <p className="text-gray-600">
                  سيتم اختبار جميع العمليات الأساسية وقياس أوقات الاستجابة
                </p>
              </div>
              <Button
                onClick={runPerformanceTests}
                disabled={isRunning}
                className="bg-purple-600 hover:bg-purple-700 flex items-center gap-2"
              >
                {isRunning ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                    جاري التشغيل...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4" />
                    تشغيل اختبارات الأداء
                  </>
                )}
              </Button>
            </div>
          </Card>

          {/* Summary */}
          {summary && (
            <Card className="mb-6 p-6 bg-blue-50 border-blue-200">
              <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                ملخص النتائج
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{summary.totalTests}</div>
                  <div className="text-sm text-blue-800">إجمالي الاختبارات</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{summary.successRate.toFixed(1)}%</div>
                  <div className="text-sm text-green-800">معدل النجاح</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{formatTime(summary.averageTime)}</div>
                  <div className="text-sm text-purple-800">متوسط الوقت</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{formatTime(summary.fastestTime)}</div>
                  <div className="text-sm text-green-800">أسرع عملية</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{formatTime(summary.slowestTime)}</div>
                  <div className="text-sm text-red-800">أبطأ عملية</div>
                </div>
              </div>
            </Card>
          )}

          {/* Results */}
          {results.length > 0 && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                نتائج الاختبارات التفصيلية
              </h3>
              <div className="space-y-3">
                {results.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      {result.success ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <AlertCircle className="w-5 h-5 text-red-500" />
                      )}
                      <div>
                        <p className="font-medium text-gray-900">{result.operation}</p>
                        {result.error && (
                          <p className="text-sm text-red-600">{result.error}</p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-bold ${getPerformanceColor(result.duration)}`}>
                        {formatTime(result.duration)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(result.timestamp).toLocaleTimeString('ar-SA')}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          )}

          {/* Performance Guidelines */}
          <Card className="mt-6 p-6 bg-green-50 border-green-200">
            <h3 className="text-lg font-semibold text-green-900 mb-4">
              معايير الأداء المتوقعة:
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-green-800">ممتاز: أقل من 500 مللي ثانية</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className="text-yellow-800">جيد: 500-1000 مللي ثانية</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                <span className="text-orange-800">مقبول: 1-2 ثانية</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-red-800">بطيء: أكثر من 2 ثانية</span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </ProtectedLayout>
  )
}
