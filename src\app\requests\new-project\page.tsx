'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { SimplifiedProjectForm } from '@/components/forms/simplified/SimplifiedProjectForm'
import { useAuth } from '@/lib/core/auth'
import {
  ChevronLeft,
  FileText,
  CheckCircle,
  Clock,
  Users,
  Target,
  HelpCircle
} from 'lucide-react'
import { ProgressIndicator, ProjectSteps } from '@/components/ui/ProgressIndicator'
import { InteractiveGuide, useInteractiveGuide, ProjectFormGuide } from '@/components/ui/InteractiveGuide'

export default function NewProjectPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  const router = useRouter()
  const { user } = useAuth()
  const { isOpen, openGuide, closeGuide } = useInteractiveGuide()

  const steps = [
    {
      number: 1,
      title: 'معلومات أساسية',
      description: 'تفاصيل المشروع الأساسية',
      icon: FileText
    },
    {
      number: 2,
      title: 'المشكلة والهدف',
      description: 'تحديد المشكلة والهدف المطلوب',
      icon: Target
    },
    {
      number: 3,
      title: 'الحل والمهام',
      description: 'تخطيط الحل والمهام المطلوبة',
      icon: CheckCircle
    },
    {
      number: 4,
      title: 'الفريق والموارد',
      description: 'تحديد الفريق والموارد المطلوبة',
      icon: Users
    },
    {
      number: 5,
      title: 'المراجعة والإرسال',
      description: 'مراجعة نهائية وإرسال المشروع',
      icon: Clock
    }
  ]

  const handleSubmit = async (data: any) => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      if (!user?.id) {
        setError('يجب تسجيل الدخول أولاً لإرسال المشروع')
        return
      }

      const projectNumber = generateProjectNumber()
      
      const projectData = {
        request_number: projectNumber,
        form_type: 'simplified_project',
        form_data: data,
        status: 'submitted',
        created_by: user.id,
        created_at: new Date().toISOString(),
      }

      const response = await fetch('/api/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(projectData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في إرسال المشروع')
      }

      setSuccess(`تم إرسال المشروع بنجاح! رقم المشروع: ${projectNumber}. سيتم مراجعة المشروع والرد عليك قريباً.`)
      
      setTimeout(() => router.push('/requests'), 3000)
      
    } catch (error) {
      console.error('Error submitting project:', error)
      setError(error instanceof Error ? error.message : 'حدث خطأ أثناء إرسال المشروع')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveDraft = async (data: any) => {
    try {
      if (!user?.id) {
        throw new Error('يجب تسجيل الدخول أولاً لحفظ المسودة')
      }

      const draftData = {
        form_type: 'simplified_project',
        form_data: data,
        user_id: user.id,
      }

      const response = await fetch('/api/requests/drafts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(draftData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في حفظ المسودة')
      }

      alert('تم حفظ المسودة بنجاح!')
    } catch (error) {
      console.error('Error saving draft:', error)
      alert(error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ المسودة')
    }
  }

  const generateProjectNumber = (): string => {
    const year = new Date().getFullYear()
    const month = String(new Date().getMonth() + 1).padStart(2, '0')
    const day = String(new Date().getDate()).padStart(2, '0')
    const time = String(Date.now()).slice(-4)
    
    return `PRJ-${year}${month}${day}-${time}`
  }

  return (
    <ProtectedLayout>
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              onClick={() => router.push('/requests/new-simplified')}
              variant="ghost"
              className="flex items-center gap-2"
            >
              <ChevronLeft className="w-4 h-4" />
              العودة للاختيار
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                مشروع جديد
              </h1>
              <p className="text-lg text-gray-600">
                إنشاء مشروع للتنفيذ المباشر
              </p>
            </div>
          </div>

          <Button
            onClick={() => openGuide('project-form')}
            variant="ghost"
            className="flex items-center gap-2 text-blue-600"
          >
            <HelpCircle className="w-4 h-4" />
            دليل الإرشاد
          </Button>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <ProgressIndicator
            steps={ProjectSteps}
            currentStep={currentStep}
            onStepClick={setCurrentStep}
            allowNavigation={true}
            className="max-w-4xl mx-auto"
          />
        </div>

        {/* Error and Success Messages */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-800">{success}</p>
          </div>
        )}

        {/* Form */}
        <Card className="p-8">
          <SimplifiedProjectForm
            currentStep={currentStep}
            onStepChange={setCurrentStep}
            onSubmit={handleSubmit}
            onSaveDraft={handleSaveDraft}
            isLoading={isLoading}
          />
        </Card>

        {/* Help Section */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            نصائح لإنشاء مشروع ناجح
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <h4 className="font-semibold mb-2">المعلومات الأساسية:</h4>
              <ul className="space-y-1">
                <li>• اختر عنوان واضح ومحدد</li>
                <li>• اكتب وصف مفصل للمشروع</li>
                <li>• حدد تواريخ واقعية</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">الفريق والموارد:</h4>
              <ul className="space-y-1">
                <li>• حدد أعضاء الفريق بوضوح</li>
                <li>• اذكر الموارد المطلوبة</li>
                <li>• قدر التكلفة بدقة</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Interactive Guide */}
        <InteractiveGuide
          title="دليل إنشاء المشروع"
          steps={ProjectFormGuide}
          isOpen={isOpen}
          onClose={closeGuide}
          position="right"
          size="md"
        />
      </div>
    </ProtectedLayout>
  )
}
