'use client'

import React from 'react'
import { Card } from '@/components/ui/Card'
import { 
  ArrowRight, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Users, 
  Target,
  Zap,
  TrendingUp,
  MessageSquare,
  FileText
} from 'lucide-react'

export function WorkflowComparison() {
  const oldWorkflow = {
    title: 'النظام السابق (معقد)',
    color: 'red',
    steps: [
      'Basic - المعلومات الأساسية',
      'Find - العثور على المشكلة', 
      'Organize - تنظيم الفريق',
      'Clarify - توضيح العمليات',
      'Understand - فهم الأسباب',
      'Select - اختيار الحل',
      'Planning - تخطيط المشروع',
      'RiskManagement - إدارة المخاطر',
      'Review - المراجعة والإرسال'
    ],
    issues: [
      'تعقيد في الخطوات (9 مراحل)',
      'صعوبة في الفهم للمستخدمين الجدد',
      'وقت طويل لإكمال النموذج',
      'عدم وضوح الفرق بين المشروع والمقترح',
      'تداخل في المراحل'
    ]
  }

  const newWorkflow = {
    title: 'النظام الجديد (مبسط)',
    color: 'green',
    projectSteps: [
      'معلومات أساسية',
      'المشكلة والهدف',
      'الحل والمهام',
      'الفريق والموارد',
      'المراجعة والإرسال'
    ],
    proposalSteps: [
      'معلومات أساسية',
      'وصف المشكلة',
      'الحل المقترح',
      'المراجعة والإرسال'
    ],
    improvements: [
      'تبسيط الخطوات (5 للمشاريع، 4 للمقترحات)',
      'وضوح في التمييز بين المشروع والمقترح',
      'سرعة في إكمال النموذج',
      'واجهة مستخدم محسنة',
      'سير عمل منطقي ومتدرج'
    ]
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          مقارنة بين النظام السابق والجديد
        </h2>
        <p className="text-gray-600">
          شاهد كيف تم تبسيط وتحسين سير العمل
        </p>
      </div>

      {/* Comparison Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Old System */}
        <Card className="p-6 border-red-200 bg-red-50">
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mb-3">
              <XCircle className="w-6 h-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold text-red-900 mb-2">
              {oldWorkflow.title}
            </h3>
            <p className="text-red-700 text-sm">
              نظام معقد بـ 9 مراحل
            </p>
          </div>

          {/* Old Steps */}
          <div className="space-y-3 mb-6">
            <h4 className="font-semibold text-red-900 mb-3">المراحل:</h4>
            {oldWorkflow.steps.map((step, index) => (
              <div key={index} className="flex items-center gap-3 text-sm">
                <div className="w-6 h-6 bg-red-200 rounded-full flex items-center justify-center text-red-800 font-semibold text-xs">
                  {index + 1}
                </div>
                <span className="text-red-800">{step}</span>
              </div>
            ))}
          </div>

          {/* Issues */}
          <div className="space-y-2">
            <h4 className="font-semibold text-red-900 mb-3">المشاكل:</h4>
            {oldWorkflow.issues.map((issue, index) => (
              <div key={index} className="flex items-start gap-2 text-sm text-red-700">
                <XCircle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                <span>{issue}</span>
              </div>
            ))}
          </div>
        </Card>

        {/* New System */}
        <Card className="p-6 border-green-200 bg-green-50">
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-3">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold text-green-900 mb-2">
              {newWorkflow.title}
            </h3>
            <p className="text-green-700 text-sm">
              نظام مبسط ومرن
            </p>
          </div>

          {/* New Steps - Projects */}
          <div className="space-y-4 mb-6">
            <div>
              <h4 className="font-semibold text-green-900 mb-3 flex items-center gap-2">
                <FileText className="w-4 h-4" />
                مراحل المشاريع (5 مراحل):
              </h4>
              <div className="space-y-2">
                {newWorkflow.projectSteps.map((step, index) => (
                  <div key={index} className="flex items-center gap-3 text-sm">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-xs">
                      {index + 1}
                    </div>
                    <span className="text-green-800">{step}</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-green-900 mb-3 flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                مراحل المقترحات (4 مراحل):
              </h4>
              <div className="space-y-2">
                {newWorkflow.proposalSteps.map((step, index) => (
                  <div key={index} className="flex items-center gap-3 text-sm">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold text-xs">
                      {index + 1}
                    </div>
                    <span className="text-green-800">{step}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Improvements */}
          <div className="space-y-2">
            <h4 className="font-semibold text-green-900 mb-3">التحسينات:</h4>
            {newWorkflow.improvements.map((improvement, index) => (
              <div key={index} className="flex items-start gap-2 text-sm text-green-700">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>{improvement}</span>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Benefits Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 text-center border-blue-200 bg-blue-50">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-4">
            <Clock className="w-6 h-6 text-blue-600" />
          </div>
          <h3 className="font-semibold text-blue-900 mb-2">توفير الوقت</h3>
          <p className="text-blue-700 text-sm">
            تقليل الوقت المطلوب لإكمال النموذج بنسبة 60%
          </p>
        </Card>

        <Card className="p-6 text-center border-purple-200 bg-purple-50">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-4">
            <Users className="w-6 h-6 text-purple-600" />
          </div>
          <h3 className="font-semibold text-purple-900 mb-2">سهولة الاستخدام</h3>
          <p className="text-purple-700 text-sm">
            واجهة أكثر بساطة ووضوحاً للمستخدمين
          </p>
        </Card>

        <Card className="p-6 text-center border-orange-200 bg-orange-50">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full mb-4">
            <Target className="w-6 h-6 text-orange-600" />
          </div>
          <h3 className="font-semibold text-orange-900 mb-2">وضوح الهدف</h3>
          <p className="text-orange-700 text-sm">
            تمييز واضح بين المشاريع والمقترحات
          </p>
        </Card>
      </div>

      {/* Migration Path */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-green-50 border-blue-200">
        <div className="text-center">
          <h3 className="text-xl font-bold text-gray-900 mb-4">
            مسار الانتقال للنظام الجديد
          </h3>
          <div className="flex items-center justify-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white font-semibold">
                1
              </div>
              <span className="text-gray-700">النظام السابق</span>
            </div>
            <ArrowRight className="w-4 h-4 text-gray-400" />
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white font-semibold">
                2
              </div>
              <span className="text-gray-700">فترة انتقالية</span>
            </div>
            <ArrowRight className="w-4 h-4 text-gray-400" />
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold">
                3
              </div>
              <span className="text-gray-700">النظام الجديد</span>
            </div>
          </div>
          <p className="text-gray-600 mt-4">
            يمكن للمستخدمين الاختيار بين النظام المتقدم والمبسط حسب احتياجاتهم
          </p>
        </div>
      </Card>

      {/* Call to Action */}
      <div className="text-center">
        <div className="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full">
          <Zap className="w-4 h-4" />
          <span className="font-medium">جرب النظام الجديد الآن!</span>
        </div>
      </div>
    </div>
  )
}
