import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

interface ConvertToProjectData {
  title: string
  description: string
  project_manager_id: string
  methodology: 'pdca' | 'agile' | 'waterfall'
  start_date: string
  end_date: string
  budget: number
  priority: 'low' | 'medium' | 'high' | 'urgent'
}

// POST - تحويل مقترح إلى مشروع
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const requestId = params.id
    const projectData: ConvertToProjectData = await request.json()

    // التحقق من صحة البيانات
    if (!projectData.title || !projectData.description || !projectData.project_manager_id) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة غير مكتملة' },
        { status: 400 }
      )
    }

    // التحقق من وجود المقترح وأنه معتمد
    const { data: existingRequest, error: requestError } = await supabaseAdmin
      .from('project_requests')
      .select('*')
      .eq('id', requestId)
      .eq('status', 'approved')
      .single()

    if (requestError || !existingRequest) {
      return NextResponse.json(
        { error: 'المقترح غير موجود أو غير معتمد' },
        { status: 404 }
      )
    }

    // التحقق من عدم وجود مشروع مرتبط بالفعل
    const { data: existingProject } = await supabaseAdmin
      .from('projects')
      .select('id')
      .eq('request_id', requestId)
      .single()

    if (existingProject) {
      return NextResponse.json(
        { error: 'هذا المقترح تم تحويله إلى مشروع بالفعل' },
        { status: 400 }
      )
    }

    // إنشاء المشروع الجديد
    const newProject = {
      request_id: requestId,
      title: projectData.title,
      description: projectData.description,
      project_manager_id: projectData.project_manager_id,
      status: 'planning',
      methodology: projectData.methodology,
      start_date: projectData.start_date,
      end_date: projectData.end_date,
      budget: projectData.budget,
      priority: projectData.priority,
      progress_percentage: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .insert([newProject])
      .select()
      .single()

    if (projectError) {
      console.error('Database error:', projectError)
      return NextResponse.json(
        { error: 'حدث خطأ في إنشاء المشروع' },
        { status: 500 }
      )
    }

    // تحديث حالة المقترح إلى "in_progress"
    const { error: updateError } = await supabaseAdmin
      .from('project_requests')
      .update({ 
        status: 'in_progress',
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId)

    if (updateError) {
      console.error('Error updating request status:', updateError)
      // لا نرجع خطأ هنا لأن المشروع تم إنشاؤه بنجاح
    }

    // إنشاء مهام أساسية للمشروع حسب المنهجية
    await createInitialTasks(project.id, projectData.methodology)

    // إنشاء إشعار لمدير المشروع
    await createProjectNotification(project.id, projectData.project_manager_id, existingRequest.title)

    return NextResponse.json({
      success: true,
      message: 'تم تحويل المقترح إلى مشروع بنجاح',
      project: {
        id: project.id,
        title: project.title,
        status: project.status
      }
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// دالة إنشاء المهام الأساسية حسب المنهجية
async function createInitialTasks(projectId: string, methodology: string) {
  let tasks: any[] = []

  switch (methodology) {
    case 'pdca':
      tasks = [
        {
          project_id: projectId,
          title: 'مرحلة التخطيط (Plan)',
          description: 'وضع خطة تفصيلية للمشروع وتحديد الأهداف والموارد',
          status: 'pending',
          priority: 'high',
          order_index: 1
        },
        {
          project_id: projectId,
          title: 'مرحلة التنفيذ (Do)',
          description: 'تنفيذ الخطة الموضوعة وتطبيق الحلول',
          status: 'pending',
          priority: 'high',
          order_index: 2
        },
        {
          project_id: projectId,
          title: 'مرحلة المراجعة (Check)',
          description: 'مراجعة النتائج وقياس الأداء مقابل الأهداف',
          status: 'pending',
          priority: 'medium',
          order_index: 3
        },
        {
          project_id: projectId,
          title: 'مرحلة التحسين (Act)',
          description: 'تطبيق التحسينات وتوثيق الدروس المستفادة',
          status: 'pending',
          priority: 'medium',
          order_index: 4
        }
      ]
      break

    case 'agile':
      tasks = [
        {
          project_id: projectId,
          title: 'إعداد المشروع',
          description: 'تكوين فريق العمل وإعداد بيئة العمل',
          status: 'pending',
          priority: 'high',
          order_index: 1
        },
        {
          project_id: projectId,
          title: 'Sprint 1',
          description: 'أول دورة تطوير في المشروع',
          status: 'pending',
          priority: 'high',
          order_index: 2
        }
      ]
      break

    case 'waterfall':
      tasks = [
        {
          project_id: projectId,
          title: 'مرحلة التحليل',
          description: 'تحليل المتطلبات والاحتياجات',
          status: 'pending',
          priority: 'high',
          order_index: 1
        },
        {
          project_id: projectId,
          title: 'مرحلة التصميم',
          description: 'تصميم الحل والهيكل العام',
          status: 'pending',
          priority: 'high',
          order_index: 2
        },
        {
          project_id: projectId,
          title: 'مرحلة التنفيذ',
          description: 'تطبيق وتنفيذ الحل',
          status: 'pending',
          priority: 'high',
          order_index: 3
        },
        {
          project_id: projectId,
          title: 'مرحلة الاختبار',
          description: 'اختبار الحل والتأكد من جودته',
          status: 'pending',
          priority: 'medium',
          order_index: 4
        },
        {
          project_id: projectId,
          title: 'مرحلة النشر',
          description: 'نشر الحل وتدريب المستخدمين',
          status: 'pending',
          priority: 'medium',
          order_index: 5
        }
      ]
      break
  }

  if (tasks.length > 0) {
    const { error } = await supabaseAdmin
      .from('tasks')
      .insert(tasks)

    if (error) {
      console.error('Error creating initial tasks:', error)
    }
  }
}

// دالة إنشاء إشعار لمدير المشروع
async function createProjectNotification(projectId: string, managerId: string, projectTitle: string) {
  const notification = {
    user_id: managerId,
    title: 'تم تعيينك كمدير مشروع جديد',
    message: `تم تعيينك كمدير للمشروع: ${projectTitle}`,
    type: 'project_assignment',
    related_id: projectId,
    is_read: false,
    created_at: new Date().toISOString()
  }

  const { error } = await supabaseAdmin
    .from('notifications')
    .insert([notification])

  if (error) {
    console.error('Error creating notification:', error)
  }
}
