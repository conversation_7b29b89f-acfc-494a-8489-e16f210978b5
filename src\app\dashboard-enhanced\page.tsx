'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useAuth } from '@/lib/core/auth'
import { 
  Zap, 
  FileText, 
  MessageSquare, 
  TrendingUp, 
  ArrowRight,
  Clock,
  Users,
  Target,
  Star,
  CheckCircle,
  BarChart3,
  Settings,
  HelpCircle
} from 'lucide-react'

export default function EnhancedDashboardPage() {
  const router = useRouter()
  const { user } = useAuth()

  const quickActions = [
    {
      id: 'new-simplified',
      title: 'طلب جديد مبسط',
      description: 'النظام الجديد المبسط - سريع وسهل',
      icon: Zap,
      color: 'green',
      href: '/requests/new-simplified',
      badge: 'جديد',
      time: '15-20 دقيقة',
      features: ['واجهة مبسطة', 'خطوات أقل', 'حفظ تلقائي']
    },
    {
      id: 'new-advanced',
      title: 'طلب جديد متقدم',
      description: 'النظام المتقدم - للمشاريع المعقدة',
      icon: FileText,
      color: 'blue',
      href: '/requests/new',
      badge: 'متقدم',
      time: '45-60 دقيقة',
      features: ['تحليل شامل', '9 مراحل', 'منهجية FOCUS-PDCA']
    },
    {
      id: 'my-requests',
      title: 'طلباتي',
      description: 'عرض ومتابعة طلباتي',
      icon: Users,
      color: 'purple',
      href: '/my-requests',
      badge: null,
      time: null,
      features: ['متابعة الحالة', 'تحديث البيانات', 'تحميل الملفات']
    },
    {
      id: 'workflow-guide',
      title: 'دليل سير العمل',
      description: 'تعلم كيفية استخدام النظام',
      icon: HelpCircle,
      color: 'orange',
      href: '/workflow',
      badge: null,
      time: null,
      features: ['شرح تفصيلي', 'أمثلة عملية', 'نصائح مفيدة']
    }
  ]

  const systemStats = [
    {
      title: 'توفير الوقت',
      value: '60%',
      description: 'تقليل الوقت المطلوب',
      icon: Clock,
      color: 'green'
    },
    {
      title: 'سهولة الاستخدام',
      value: '80%',
      description: 'تحسن في تجربة المستخدم',
      icon: Target,
      color: 'blue'
    },
    {
      title: 'معدل الإكمال',
      value: '90%',
      description: 'نسبة إكمال النماذج',
      icon: CheckCircle,
      color: 'purple'
    },
    {
      title: 'رضا المستخدمين',
      value: '4.8/5',
      description: 'تقييم المستخدمين',
      icon: Star,
      color: 'yellow'
    }
  ]

  const getColorClasses = (color: string) => ({
    card: {
      green: 'border-green-200 hover:border-green-300 bg-green-50 hover:bg-green-100',
      blue: 'border-blue-200 hover:border-blue-300 bg-blue-50 hover:bg-blue-100',
      purple: 'border-purple-200 hover:border-purple-300 bg-purple-50 hover:bg-purple-100',
      orange: 'border-orange-200 hover:border-orange-300 bg-orange-50 hover:bg-orange-100',
      yellow: 'border-yellow-200 hover:border-yellow-300 bg-yellow-50 hover:bg-yellow-100'
    }[color],
    icon: {
      green: 'text-green-600 bg-green-100',
      blue: 'text-blue-600 bg-blue-100',
      purple: 'text-purple-600 bg-purple-100',
      orange: 'text-orange-600 bg-orange-100',
      yellow: 'text-yellow-600 bg-yellow-100'
    }[color],
    badge: {
      green: 'bg-green-100 text-green-800',
      blue: 'bg-blue-100 text-blue-800',
      purple: 'bg-purple-100 text-purple-800',
      orange: 'bg-orange-100 text-orange-800',
      yellow: 'bg-yellow-100 text-yellow-800'
    }[color],
    button: {
      green: 'bg-green-600 hover:bg-green-700 text-white',
      blue: 'bg-blue-600 hover:bg-blue-700 text-white',
      purple: 'bg-purple-600 hover:bg-purple-700 text-white',
      orange: 'bg-orange-600 hover:bg-orange-700 text-white',
      yellow: 'bg-yellow-600 hover:bg-yellow-700 text-white'
    }[color]
  })

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Welcome Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            مرحباً بك في نظام إدارة المشاريع المحسن
          </h1>
          <p className="text-xl text-gray-600 mb-2">
            مرحباً {user?.user_metadata?.name || user?.email}
          </p>
          <p className="text-gray-500">
            اختر الطريقة المناسبة لإنشاء طلبك الجديد
          </p>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">الإجراءات السريعة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action) => {
              const Icon = action.icon
              const colors = getColorClasses(action.color)

              return (
                <Card
                  key={action.id}
                  className={`p-6 cursor-pointer transition-all duration-300 hover:shadow-lg hover:transform hover:scale-105 ${colors.card}`}
                  onClick={() => router.push(action.href)}
                >
                  <div className="text-center">
                    <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${colors.icon}`}>
                      <Icon className="w-8 h-8" />
                    </div>
                    
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <h3 className="font-bold text-gray-900">{action.title}</h3>
                      {action.badge && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors.badge}`}>
                          {action.badge}
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-600 text-sm mb-4">{action.description}</p>
                    
                    {action.time && (
                      <div className="flex items-center justify-center gap-1 mb-3 text-xs text-gray-500">
                        <Clock className="w-3 h-3" />
                        <span>{action.time}</span>
                      </div>
                    )}
                    
                    <div className="space-y-1 mb-4">
                      {action.features.map((feature, index) => (
                        <div key={index} className="flex items-center justify-center gap-1 text-xs text-gray-600">
                          <CheckCircle className="w-3 h-3 text-green-500" />
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-center gap-2 text-sm font-medium text-gray-700">
                      ابدأ الآن
                      <ArrowRight className="w-4 h-4" />
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>
        </div>

        {/* System Statistics */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">إحصائيات النظام المحسن</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {systemStats.map((stat, index) => {
              const Icon = stat.icon
              const colors = getColorClasses(stat.color)

              return (
                <Card key={index} className={`p-6 text-center ${colors.card}`}>
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full mb-4 ${colors.icon}`}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</h3>
                  <p className="font-medium text-gray-700 mb-1">{stat.title}</p>
                  <p className="text-sm text-gray-500">{stat.description}</p>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Comparison Banner */}
        <Card className="p-8 bg-gradient-to-r from-blue-50 to-green-50 border-blue-200">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              شاهد التحسينات الجديدة
            </h2>
            <p className="text-gray-600 mb-6">
              قارن بين النظام السابق والجديد واكتشف كيف وفرنا وقتك وجهدك
            </p>
            <div className="flex justify-center gap-4">
              <Button
                onClick={() => router.push('/workflow-comparison')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                مقارنة التحسينات
              </Button>
              <Button
                onClick={() => router.push('/workflow')}
                variant="ghost"
                className="px-6 py-3"
              >
                دليل سير العمل
              </Button>
            </div>
          </div>
        </Card>

        {/* Quick Links */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/projects')}>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">إدارة المشاريع</h3>
                <p className="text-sm text-gray-600">متابعة المشاريع الجارية</p>
              </div>
            </div>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/reports')}>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <FileText className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">التقارير</h3>
                <p className="text-sm text-gray-600">تقارير الأداء والإنجاز</p>
              </div>
            </div>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/settings')}>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Settings className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">الإعدادات</h3>
                <p className="text-sm text-gray-600">إعدادات النظام والحساب</p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </ProtectedLayout>
  )
}
