'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  HelpCircle, 
  X, 
  ChevronRight, 
  ChevronLeft, 
  Lightbulb,
  CheckCircle,
  AlertCircle,
  Info,
  Star
} from 'lucide-react'

interface GuideStep {
  id: string
  title: string
  content: string
  type?: 'info' | 'tip' | 'warning' | 'success'
  example?: string
  image?: string
}

interface InteractiveGuideProps {
  title: string
  steps: GuideStep[]
  isOpen: boolean
  onClose: () => void
  position?: 'right' | 'left' | 'center'
  size?: 'sm' | 'md' | 'lg'
}

export function InteractiveGuide({
  title,
  steps,
  isOpen,
  onClose,
  position = 'right',
  size = 'md'
}: InteractiveGuideProps) {
  const [currentStep, setCurrentStep] = useState(0)

  if (!isOpen) return null

  const currentGuideStep = steps[currentStep]

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'tip':
        return <Lightbulb className="w-5 h-5 text-yellow-600" />
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-orange-600" />
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      default:
        return <Info className="w-5 h-5 text-blue-600" />
    }
  }

  const getTypeClasses = (type: string) => {
    switch (type) {
      case 'tip':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'warning':
        return 'bg-orange-50 border-orange-200 text-orange-800'
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800'
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800'
    }
  }

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-80'
      case 'lg':
        return 'w-96'
      default:
        return 'w-88'
    }
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'left':
        return 'left-4'
      case 'center':
        return 'left-1/2 transform -translate-x-1/2'
      default:
        return 'right-4'
    }
  }

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex)
  }

  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-25 pointer-events-auto"
        onClick={onClose}
      />
      
      {/* Guide Panel */}
      <div className={`
        absolute top-20 ${getPositionClasses()} ${getSizeClasses()}
        pointer-events-auto
      `}>
        <Card className="shadow-2xl border-gray-300">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center gap-2">
              <HelpCircle className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">{title}</h3>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Progress Dots */}
          <div className="flex justify-center gap-2 p-3 bg-gray-50 border-b border-gray-200">
            {steps.map((_, index) => (
              <button
                key={index}
                onClick={() => goToStep(index)}
                className={`
                  w-2 h-2 rounded-full transition-all duration-200
                  ${index === currentStep 
                    ? 'bg-blue-600 w-6' 
                    : index < currentStep 
                      ? 'bg-green-600' 
                      : 'bg-gray-300'
                  }
                `}
              />
            ))}
          </div>

          {/* Content */}
          <div className="p-4">
            {/* Step Header */}
            <div className={`
              flex items-center gap-2 p-3 rounded-lg mb-4 border
              ${getTypeClasses(currentGuideStep.type || 'info')}
            `}>
              {getTypeIcon(currentGuideStep.type || 'info')}
              <h4 className="font-medium">{currentGuideStep.title}</h4>
            </div>

            {/* Step Content */}
            <div className="space-y-3">
              <p className="text-gray-700 text-sm leading-relaxed">
                {currentGuideStep.content}
              </p>

              {/* Example */}
              {currentGuideStep.example && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-2">
                    <Star className="w-4 h-4 text-yellow-600" />
                    <span className="text-sm font-medium text-gray-700">مثال:</span>
                  </div>
                  <p className="text-sm text-gray-600 italic">
                    {currentGuideStep.example}
                  </p>
                </div>
              )}

              {/* Image */}
              {currentGuideStep.image && (
                <div className="rounded-lg overflow-hidden border border-gray-200">
                  <img 
                    src={currentGuideStep.image} 
                    alt={currentGuideStep.title}
                    className="w-full h-auto"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                {currentStep + 1} من {steps.length}
              </span>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={prevStep}
                disabled={currentStep === 0}
                variant="ghost"
                size="sm"
                className="flex items-center gap-1"
              >
                <ChevronLeft className="w-4 h-4" />
                السابق
              </Button>

              {currentStep < steps.length - 1 ? (
                <Button
                  onClick={nextStep}
                  size="sm"
                  className="flex items-center gap-1"
                >
                  التالي
                  <ChevronRight className="w-4 h-4" />
                </Button>
              ) : (
                <Button
                  onClick={onClose}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                >
                  إنهاء
                </Button>
              )}
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}

// Hook for managing guide state
export function useInteractiveGuide() {
  const [isOpen, setIsOpen] = useState(false)
  const [currentGuide, setCurrentGuide] = useState<string | null>(null)

  const openGuide = (guideId: string) => {
    setCurrentGuide(guideId)
    setIsOpen(true)
  }

  const closeGuide = () => {
    setIsOpen(false)
    setCurrentGuide(null)
  }

  return {
    isOpen,
    currentGuide,
    openGuide,
    closeGuide
  }
}

// Predefined guides for common scenarios
export const ProjectFormGuide: GuideStep[] = [
  {
    id: 'welcome',
    title: 'مرحباً بك في نموذج المشروع المبسط',
    content: 'سنرشدك خلال 5 خطوات بسيطة لإنشاء مشروع جديد. كل خطوة تركز على جانب محدد من المشروع.',
    type: 'info',
    example: 'ستحتاج حوالي 15-20 دقيقة لإكمال النموذج'
  },
  {
    id: 'basic-info',
    title: 'المعلومات الأساسية',
    content: 'ابدأ بكتابة عنوان واضح ووصف مختصر للمشروع. اختر القسم المسؤول وحدد التواريخ المتوقعة.',
    type: 'tip',
    example: 'عنوان جيد: "تطوير نظام إدارة المخزون الإلكتروني"'
  },
  {
    id: 'problem-goal',
    title: 'المشكلة والهدف',
    content: 'اشرح المشكلة التي يحلها المشروع والهدف المطلوب تحقيقه. كن محدداً وواضحاً.',
    type: 'warning',
    example: 'تجنب الأهداف العامة مثل "تحسين الأداء" واستخدم أهداف قابلة للقياس'
  },
  {
    id: 'solution-tasks',
    title: 'الحل والمهام',
    content: 'حدد الحل المقترح وقسمه إلى مهام رئيسية. كل مهمة يجب أن تكون واضحة ومحددة الوقت.',
    type: 'tip',
    example: 'مهمة جيدة: "تصميم قاعدة البيانات - أسبوعين"'
  },
  {
    id: 'team-resources',
    title: 'الفريق والموارد',
    content: 'حدد مدير المشروع وأعضاء الفريق. اذكر الموارد المطلوبة والميزانية المقدرة.',
    type: 'info',
    example: 'تأكد من توفر جميع أعضاء الفريق خلال فترة المشروع'
  },
  {
    id: 'review',
    title: 'المراجعة والإرسال',
    content: 'راجع جميع المعلومات قبل الإرسال. يمكنك حفظ المسودة والعودة لاحقاً إذا احتجت وقت إضافي.',
    type: 'success',
    example: 'بعد الإرسال، ستحصل على رقم مرجعي للمشروع'
  }
]

export const ProposalFormGuide: GuideStep[] = [
  {
    id: 'welcome',
    title: 'مرحباً بك في نموذج المقترح المبسط',
    content: 'سنرشدك خلال 4 خطوات لإنشاء مقترح جديد. المقترح يختلف عن المشروع في أنه فكرة تحتاج دراسة وموافقة.',
    type: 'info'
  },
  {
    id: 'basic-info',
    title: 'المعلومات الأساسية',
    content: 'أدخل عنوان المقترح ووصف مختصر. حدد القسم المختص ومعلومات مقدم المقترح.',
    type: 'tip',
    example: 'عنوان جيد: "اقتراح تطبيق نظام العمل عن بُعد"'
  },
  {
    id: 'problem',
    title: 'وصف المشكلة',
    content: 'اشرح المشكلة أو الفرصة التحسينية بالتفصيل. حدد التأثير الحالي والمناطق المتأثرة.',
    type: 'warning',
    example: 'استخدم أرقام وإحصائيات لتوضيح حجم المشكلة'
  },
  {
    id: 'solution',
    title: 'الحل المقترح',
    content: 'اقترح حلول متعددة وقارن بينها. حدد التوصية النهائية والفوائد المتوقعة.',
    type: 'success',
    example: 'اقترح 2-3 حلول مختلفة مع ذكر مزايا وعيوب كل حل'
  }
]
