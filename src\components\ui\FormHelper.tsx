'use client'

import React, { useState } from 'react'
import { HelpCircle, X, ChevronDown, ChevronUp, Lightbulb, CheckCircle, AlertCircle } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

// أنواع المساعدة
export type HelpType = 'tip' | 'example' | 'warning' | 'info'

export interface HelpContent {
  type: HelpType
  title: string
  content: string
  example?: string
  links?: Array<{
    text: string
    url: string
  }>
}

interface FormHelperProps {
  title: string
  content: HelpContent[]
  position?: 'right' | 'left' | 'bottom'
  trigger?: 'click' | 'hover'
  className?: string
}

export function FormHelper({ 
  title, 
  content, 
  position = 'right', 
  trigger = 'click',
  className = '' 
}: FormHelperProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<number[]>([])

  const getIcon = (type: HelpType) => {
    switch (type) {
      case 'tip':
        return <Lightbulb className="w-4 h-4 text-yellow-600" />
      case 'example':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-red-600" />
      case 'info':
        return <HelpCircle className="w-4 h-4 text-blue-600" />
    }
  }

  const getTypeClasses = (type: HelpType) => {
    switch (type) {
      case 'tip':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'example':
        return 'bg-green-50 border-green-200 text-green-800'
      case 'warning':
        return 'bg-red-50 border-red-200 text-red-800'
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800'
    }
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'left':
        return 'right-full mr-2'
      case 'bottom':
        return 'top-full mt-2'
      default:
        return 'left-full ml-2'
    }
  }

  const toggleExpanded = (index: number) => {
    setExpandedItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    )
  }

  const handleTrigger = () => {
    if (trigger === 'click') {
      setIsOpen(!isOpen)
    }
  }

  const handleMouseEnter = () => {
    if (trigger === 'hover') {
      setIsOpen(true)
    }
  }

  const handleMouseLeave = () => {
    if (trigger === 'hover') {
      setIsOpen(false)
    }
  }

  return (
    <div className={`relative inline-block ${className}`}>
      {/* Trigger Button */}
      <button
        onClick={handleTrigger}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
        title={title}
      >
        <HelpCircle className="w-4 h-4" />
      </button>

      {/* Help Panel */}
      {isOpen && (
        <div className={`
          absolute z-50 w-80 ${getPositionClasses()}
          ${position === 'bottom' ? 'left-0' : ''}
        `}>
          <Card className="shadow-lg border-gray-300 max-h-96 overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
              <h3 className="font-semibold text-gray-900 text-sm">{title}</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {/* Content */}
            <div className="p-3 space-y-3">
              {content.map((item, index) => (
                <div key={index} className={`border rounded-lg ${getTypeClasses(item.type)}`}>
                  <button
                    onClick={() => toggleExpanded(index)}
                    className="w-full p-3 flex items-center justify-between text-left"
                  >
                    <div className="flex items-center gap-2">
                      {getIcon(item.type)}
                      <span className="font-medium text-sm">{item.title}</span>
                    </div>
                    {expandedItems.includes(index) ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </button>

                  {expandedItems.includes(index) && (
                    <div className="px-3 pb-3 space-y-2">
                      <p className="text-sm">{item.content}</p>
                      
                      {item.example && (
                        <div className="bg-white bg-opacity-50 rounded p-2">
                          <p className="text-xs font-medium mb-1">مثال:</p>
                          <p className="text-xs italic">{item.example}</p>
                        </div>
                      )}

                      {item.links && item.links.length > 0 && (
                        <div className="space-y-1">
                          <p className="text-xs font-medium">روابط مفيدة:</p>
                          {item.links.map((link, linkIndex) => (
                            <a
                              key={linkIndex}
                              href={link.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="block text-xs underline hover:no-underline"
                            >
                              {link.text}
                            </a>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}

// مكون مساعد مبسط للنصائح السريعة
interface QuickTipProps {
  tip: string
  className?: string
}

export function QuickTip({ tip, className = '' }: QuickTipProps) {
  return (
    <div className={`flex items-start gap-2 p-2 bg-blue-50 border border-blue-200 rounded text-blue-800 text-xs ${className}`}>
      <Lightbulb className="w-3 h-3 mt-0.5 flex-shrink-0" />
      <span>{tip}</span>
    </div>
  )
}

// مكون لعرض أمثلة
interface ExampleBoxProps {
  title: string
  example: string
  className?: string
}

export function ExampleBox({ title, example, className = '' }: ExampleBoxProps) {
  return (
    <div className={`p-3 bg-green-50 border border-green-200 rounded ${className}`}>
      <div className="flex items-center gap-2 mb-2">
        <CheckCircle className="w-4 h-4 text-green-600" />
        <span className="font-medium text-green-800 text-sm">{title}</span>
      </div>
      <p className="text-green-700 text-xs italic">{example}</p>
    </div>
  )
}

// مكون للتحذيرات
interface WarningBoxProps {
  warning: string
  className?: string
}

export function WarningBox({ warning, className = '' }: WarningBoxProps) {
  return (
    <div className={`flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded text-red-800 text-sm ${className}`}>
      <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
      <span>{warning}</span>
    </div>
  )
}

// Hook لإدارة المساعدة التفاعلية
export function useFormHelp() {
  const [activeHelp, setActiveHelp] = useState<string | null>(null)
  const [helpHistory, setHelpHistory] = useState<string[]>([])

  const showHelp = (helpId: string) => {
    setActiveHelp(helpId)
    if (!helpHistory.includes(helpId)) {
      setHelpHistory(prev => [...prev, helpId])
    }
  }

  const hideHelp = () => {
    setActiveHelp(null)
  }

  const hasSeenHelp = (helpId: string) => {
    return helpHistory.includes(helpId)
  }

  return {
    activeHelp,
    showHelp,
    hideHelp,
    hasSeenHelp,
    helpHistory
  }
}

// محتوى المساعدة المحدد مسبقاً للنماذج
export const FormHelpContent = {
  // مساعدة للمعلومات الأساسية
  basicInfo: [
    {
      type: 'tip' as HelpType,
      title: 'اختيار عنوان واضح',
      content: 'اختر عنوان يوضح الهدف من المشروع بشكل مباشر ومفهوم',
      example: 'تطوير نظام إدارة المخزون الإلكتروني'
    },
    {
      type: 'example' as HelpType,
      title: 'وصف فعال للمشروع',
      content: 'اكتب وصف يشمل المشكلة والحل والفوائد المتوقعة',
      example: 'مشروع لتطوير نظام إلكتروني لإدارة المخزون يهدف إلى تقليل الأخطاء وزيادة الكفاءة'
    }
  ],

  // مساعدة للمؤشرات
  indicators: [
    {
      type: 'info' as HelpType,
      title: 'اختيار المؤشر المناسب',
      content: 'اختر مؤشر قابل للقياس ومرتبط مباشرة بالمشكلة',
      example: 'نسبة الأخطاء في المخزون، وقت معالجة الطلبات'
    },
    {
      type: 'warning' as HelpType,
      title: 'تحديد القيم بدقة',
      content: 'تأكد من دقة القيم الحالية والمستهدفة واعتمد على بيانات موثقة'
    }
  ],

  // مساعدة لتنظيم الفريق
  teamOrganization: [
    {
      type: 'tip' as HelpType,
      title: 'اختيار قائد الفريق',
      content: 'اختر شخص لديه الخبرة والوقت الكافي لقيادة المشروع',
      example: 'مدير القسم أو موظف كبير لديه خبرة في المجال'
    },
    {
      type: 'example' as HelpType,
      title: 'تشكيل فريق متوازن',
      content: 'ضم أعضاء من أقسام مختلفة حسب طبيعة المشروع',
      example: 'فريق تطوير النظام: مطور، محلل أعمال، مختبر، مستخدم نهائي'
    }
  ],

  // مساعدة لتحليل الأسباب
  rootCauseAnalysis: [
    {
      type: 'info' as HelpType,
      title: 'طريقة الأسباب الخمسة',
      content: 'اسأل "لماذا؟" خمس مرات للوصول للسبب الجذري',
      example: 'لماذا تحدث أخطاء المخزون؟ → لماذا لا يتم التحديث؟ → وهكذا...'
    },
    {
      type: 'tip' as HelpType,
      title: 'مخطط عظم السمك',
      content: 'صنف الأسباب حسب: الأشخاص، العمليات، المعدات، البيئة، المواد، القياس'
    }
  ]
}
