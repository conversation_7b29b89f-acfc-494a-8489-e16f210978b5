'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { ChevronRight, ChevronLeft, Save, Send, HelpCircle, BookOpen, CheckCircle, AlertCircle } from 'lucide-react'
import { EnhancedHelpSystem } from '@/components/ui/EnhancedHelpSystem'
import { StepByStepGuide } from '@/components/ui/StepByStepGuide'
import { QuickTip, ExampleBox, WarningBox } from '@/components/ui/FormHelper'
import { useAutoDraft } from '@/hooks/useAutoDraft'
import { useAuth } from '@/lib/core/auth'


// استيراد المكونات الموحدة
import { AdaptiveBasicStep } from './unified-steps/AdaptiveBasicStep'

import { AdaptiveFindStep } from './unified-steps/AdaptiveFindStep'
import { AdaptiveOrganizeStep } from './unified-steps/AdaptiveOrganizeStep'
import { AdaptiveClarifyStep } from './unified-steps/AdaptiveClarifyStep'
import { AdaptiveUnderstandStep } from './unified-steps/AdaptiveUnderstandStep'
import { AdaptiveSelectStep } from './unified-steps/AdaptiveSelectStep'
import { AdaptiveProjectPlanningStep } from './unified-steps/AdaptiveProjectPlanningStep'
import { AdaptiveRiskManagementStep } from './unified-steps/AdaptiveRiskManagementStep'
import { AdaptiveQuickWinStep } from './unified-steps/AdaptiveQuickWinStep'

import { AdaptiveReviewStep } from './unified-steps/AdaptiveReviewStep'


// أنواع النماذج المدعومة
export type FormType = 'enhanced_improvement' | 'suggestion' | 'quick_win'

// واجهة البيانات الأساسية المشتركة
interface BaseFormData {
  // Find - مشترك 100%
  problemDescription: string
  indicatorName: string
  currentValue: number
  targetValue: number
  improvementDirection: 'increase' | 'decrease' | ''
  unit: string
  dataSource: string
  measurementMethod: string
  calculatedGap: number
  isValidDirection?: boolean
  attachments: File[]
  
  // Organize - مشترك مع مرونة
  responsibleDepartment: string
  teamLeader: {
    name: string
    phone: string
    email: string
  }
  teamMembers: Array<{
    name: string
    role: string
    phone: string
    department: string
  }>
  participatingDepartments: string[]
  
  // Clarify - مشترك 100%
  processDescription: string
  processMap: File | null
  problemScope: string
  affectedOutputs: string[]
  
  // Understand - مشترك 100%
  analysisMethod: 'five_whys' | 'fishbone' | 'root_cause_analysis'
  rootCause: string
  fiveWhysSteps: string[]
  fishboneFactors: {
    people: string[]
    process: string[]
    equipment: string[]
    environment: string[]
    materials: string[]
    measurement: string[]
  }
}

// واجهة للتحسين الشامل
export interface EnhancedImprovementData extends BaseFormData {
  // Basic Info - خاص بالتحسين الشامل
  projectName: string
  projectDescription: string
  startDate: string
  endDate: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  // Select - حل واحد للتحسين الشامل
  selectedSolution: {
    description: string
    justification: string
    expectedBenefits: string
    estimatedCost: number
    implementationTime: string
  }
  
  // Project Planning - تخطيط المشروع (مرحلة جديدة)
  projectTasks: Array<{
    title: string
    description: string
    assignee: string
    startDate: string
    endDate: string
    status: 'pending' | 'in_progress' | 'completed'
  }>
  requiredResources: Array<{
    type: 'human' | 'financial' | 'equipment' | 'software' | 'other'
    description: string
    quantity: number
    cost: number
    unit: string
  }>
  
  // Risk Management - إدارة المخاطر المبسطة
  risks?: Array<{
    id: string
    description: string
    probability: 'low' | 'medium' | 'high'
    impact: 'low' | 'medium' | 'high'
    mitigation?: string
  }>
}

// واجهة للمقترحات
export interface SuggestionData extends BaseFormData {
  // Select - حلول متعددة للمقترحات
  suggestedSolutions: Array<{
    id: string
    title: string
    description: string
    justification: string
    expectedBenefits: string
    feasibilityScore: number
    impactScore: number
    priority: 'low' | 'medium' | 'high'
  }>
}

// واجهة لكويك وين
export interface QuickWinData extends BaseFormData {
  // Basic Info - معلومات مبسطة لكويك وين
  projectTitle: string
  section: string
  projectExecutor: {
    name: string
    phone: string
    email: string
    startDate?: string
  }
  
  // Select - حل مبسط لكويك وين
  solution: {
    description: string
    tasks: string[]
    implementationWeeks: number // حد أقصى 4 أسابيع
    estimatedCost: number
  }
  
  // مؤشرات متقدمة لكويك وين
  advancedIndicators: Array<{
    name: string
    currentValue: number
    targetValue: number
    direction: 'increase' | 'decrease'
    unit: string
    dataSource: string
    measurementMethod: string
    calculatedGap: number
  }>
  
  // مخاطر مبسطة لكويك وين
  risks?: Array<{
    id: string
    description: string
    probability: 'low' | 'medium' | 'high'
    impact: 'low' | 'medium' | 'high'
    mitigation?: string
  }>
}

// نوع البيانات الموحد
export type UnifiedFormData = EnhancedImprovementData | SuggestionData | QuickWinData

interface UnifiedProjectFormProps {
  formType: FormType
  onSubmit: (data: UnifiedFormData) => void
  onSaveDraft: (data: UnifiedFormData) => void
  initialData?: Partial<UnifiedFormData>
  isLoading?: boolean
}

export function UnifiedProjectForm({
  formType,
  onSubmit,
  onSaveDraft,
  initialData,
  isLoading = false
}: UnifiedProjectFormProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [showHelpSystem, setShowHelpSystem] = useState(false)
  const [showStepGuide, setShowStepGuide] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isValidating, setIsValidating] = useState(false)
  const [formData, setFormData] = useState<UnifiedFormData>(() => {
    // إنشاء البيانات الافتراضية حسب نوع النموذج
    const baseData: BaseFormData = {
      // Find
      problemDescription: '',
      indicatorName: '',
      currentValue: 0,
      targetValue: 0,
      improvementDirection: '',
      unit: '',
      dataSource: '',
      measurementMethod: '',
      calculatedGap: 0,
      attachments: [],
      
      // Organize
      responsibleDepartment: '',
      teamLeader: { name: '', phone: '', email: '' },
      teamMembers: [],
      participatingDepartments: [],
      
      // Clarify
      processDescription: '',
      processMap: null,
      problemScope: '',
      affectedOutputs: [],
      
      // Understand
      analysisMethod: 'five_whys',
      rootCause: '',
      fiveWhysSteps: ['', '', '', '', ''],
      fishboneFactors: {
        people: [],
        process: [],
        equipment: [],
        environment: [],
        materials: [],
        measurement: []
      }
    }

    switch (formType) {
      case 'enhanced_improvement':
        return {
          ...baseData,
          // Basic Info
          projectName: '',
          projectDescription: '',
          startDate: '',
          endDate: '',
          priority: 'medium',
          
          // Select
          selectedSolution: {
            description: '',
            justification: '',
            expectedBenefits: '',
            estimatedCost: 0,
            implementationTime: ''
          },
          
          // Project Planning
          projectTasks: [],
          requiredResources: [],
          
          // Risk Management
          risks: []
        } as EnhancedImprovementData

      case 'suggestion':
        return {
          ...baseData,
          // Select
          suggestedSolutions: [],
          
          // Finalize
          recommendations: '',
          nextSteps: []
        } as SuggestionData

      case 'quick_win':
        return {
          ...baseData,
          // Basic Info
          projectTitle: '',
          section: '',
          projectExecutor: { name: '', phone: '', email: '' },
          
          // Select
          solution: {
            description: '',
            tasks: [],
            implementationWeeks: 1,
            estimatedCost: 0
          },
          
          // Advanced Indicators
          advancedIndicators: [],
          
          // مخاطر مبسطة
          risks: []
        } as QuickWinData

      default:
        return baseData as UnifiedFormData
    }
  })

  // تعريف الخطوات مبكراً لاستخدامها في hooks
  const getSteps = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return [
          { number: 1, title: 'Basic', description: 'المعلومات الأساسية' },
          { number: 2, title: 'Find', description: 'العثور على المشكلة' },
          { number: 3, title: 'Organize', description: 'تنظيم الفريق' },
          { number: 4, title: 'Clarify', description: 'توضيح العمليات' },
          { number: 5, title: 'Understand', description: 'فهم الأسباب' },
          { number: 6, title: 'Select', description: 'اختيار الحل' },
          { number: 7, title: 'Planning', description: 'تخطيط المشروع' },
          { number: 8, title: 'RiskManagement', description: 'إدارة المخاطر' },
          { number: 9, title: 'Review', description: 'المراجعة والإرسال' }
        ]
      case 'quick_win':
        return [
          { number: 1, title: 'QuickWin', description: 'معلومات كويك وين' },
          { number: 2, title: 'Find', description: 'العثور على المشكلة' },
          { number: 3, title: 'Organize', description: 'تنظيم الفريق' },
          { number: 4, title: 'Understand', description: 'فهم الأسباب' },
          { number: 5, title: 'Select', description: 'اختيار الحل' },
          { number: 6, title: 'Review', description: 'المراجعة والإرسال' }
        ]
      case 'suggestion':
        return [
          { number: 1, title: 'Find', description: 'العثور على المشكلة' },
          { number: 2, title: 'Organize', description: 'تنظيم الفريق' },
          { number: 3, title: 'Clarify', description: 'توضيح العمليات' },
          { number: 4, title: 'Understand', description: 'فهم الأسباب' },
          { number: 5, title: 'Select', description: 'اختيار الحل' },
          { number: 6, title: 'Planning', description: 'تخطيط المشروع' },
          { number: 7, title: 'Review', description: 'المراجعة والإرسال' }
        ]
      default:
        return [
          { number: 1, title: 'Basic', description: 'المعلومات الأساسية' },
          { number: 2, title: 'Review', description: 'المراجعة والإرسال' }
        ]
    }
  }

  const steps = getSteps()
  const totalSteps = steps.length

  // التحسينات الجديدة - مبسطة لتجنب إعادة الرندر
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [stepsWithErrors, setStepsWithErrors] = useState<number[]>([])

  const markStepCompleted = useCallback((stepNumber: number) => {
    setCompletedSteps(prev => {
      if (!prev.includes(stepNumber)) {
        return [...prev, stepNumber]
      }
      return prev
    })
    setStepsWithErrors(prev => prev.filter(step => step !== stepNumber))
  }, [])

  const markStepWithError = useCallback((stepNumber: number) => {
    setStepsWithErrors(prev => {
      if (!prev.includes(stepNumber)) {
        return [...prev, stepNumber]
      }
      return prev
    })
    setCompletedSteps(prev => prev.filter(step => step !== stepNumber))
  }, [])

  const clearStepError = useCallback((stepNumber: number) => {
    setStepsWithErrors(prev => prev.filter(step => step !== stepNumber))
  }, [])

  // الحصول على معلومات المستخدم
  const { user } = useAuth()

  // نظام الحفظ التلقائي للمسودات
  const autoDraft = useAutoDraft({
    formData,
    formType,
    userId: user?.id || null,
    onSaveDraft,
    autoSaveInterval: 30,
    enabled: !!user?.id
  })



  // دالة للحصول على رسالة خطأ مفصلة
  const getDetailedErrorMessage = (stepTitle: string, errors: Record<string, string>) => {
    const errorFields = Object.keys(errors)
    const suggestions: string[] = []

    switch (stepTitle) {
      case 'Basic':
        if (errorFields.includes('projectName')) {
          suggestions.push('💡 اختر عنوان واضح يوضح الهدف من المشروع')
        }
        if (errorFields.includes('projectDescription')) {
          suggestions.push('💡 اكتب وصف يشمل المشكلة والحل والفوائد المتوقعة')
        }
        break
      case 'Find':
        if (errorFields.includes('problemDescription')) {
          suggestions.push('💡 اوصف المشكلة بوضوح مع ذكر تأثيرها على العمل')
        }
        if (errorFields.includes('indicatorName')) {
          suggestions.push('💡 اختر مؤشر قابل للقياس مثل "نسبة الأخطاء" أو "وقت الاستجابة"')
        }
        break
      case 'Organize':
        if (errorFields.includes('teamLeader.name')) {
          suggestions.push('💡 اختر قائد فريق لديه الخبرة والوقت الكافي')
        }
        if (errorFields.includes('teamLeader.phone')) {
          suggestions.push('💡 تأكد من رقم الهاتف يبدأ بـ 05 ويتكون من 10 أرقام')
        }
        break
      case 'Understand':
        if (errorFields.includes('rootCause')) {
          suggestions.push('💡 ابحث عن السبب الجذري وليس الأعراض فقط')
          suggestions.push('💡 استخدم طريقة الأسباب الخمسة أو مخطط عظم السمك')
        }
        break
    }

    return {
      errorCount: errorFields.length,
      suggestions,
      fields: errorFields
    }
  }

  // دالة لعرض رسالة خطأ محسنة
  const showEnhancedErrorDialog = (stepTitle: string, errorDetails: any) => {
    const { errorCount, suggestions, fields } = errorDetails

    let message = `❌ يوجد ${errorCount} خطأ في مرحلة "${stepTitle}"\n\n`

    if (suggestions.length > 0) {
      message += '📋 اقتراحات للتحسين:\n'
      suggestions.forEach(suggestion => {
        message += `${suggestion}\n`
      })
    }

    message += '\n🔍 يرجى مراجعة الحقول المميزة باللون الأحمر وإكمالها'

    alert(message)
  }

  // تحديث البيانات من initialData
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }))
    }
  }, [initialData])

  // حساب الفجوة تلقائياً
  useEffect(() => {
    if ('currentValue' in formData && 'targetValue' in formData) {
      const gap = Math.abs(formData.targetValue - formData.currentValue)
      if (formData.calculatedGap !== gap) {
        setFormData(prev => ({ ...prev, calculatedGap: gap }))
      }
    }
  }, [formData.currentValue, formData.targetValue])



  const updateFormData = (field: string, value: unknown) => {
    // التعامل مع المسارات المتداخلة مثل projectExecutor.name
    if (field.includes('.')) {
      const parts = field.split('.')
      const mainField = parts[0]
      const nestedField = parts[1]
      
      setFormData(prev => ({
        ...prev,
        [mainField]: {
          ...prev[mainField as keyof typeof prev],
          [nestedField]: value
        }
      }))
    } else {
      // التعامل مع الحقول العادية
      setFormData(prev => ({ ...prev, [field]: value }))
    }

    // إزالة الخطأ عند تحديث الحقل
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const validateCurrentStep = (): boolean => {
    const stepTitle = steps[currentStep - 1]?.title
    const newErrors: Record<string, string> = {}

    switch (stepTitle) {
      case 'Basic':
        if (formType === 'enhanced_improvement') {
          const data = formData as EnhancedImprovementData
          if (!data.projectName?.trim()) {
            newErrors.projectName = 'اسم المشروع مطلوب'
          } else if (data.projectName.length < 5) {
            newErrors.projectName = 'اسم المشروع يجب أن يكون 5 أحرف على الأقل'
          }

          if (!data.projectDescription?.trim()) {
            newErrors.projectDescription = 'وصف المشروع مطلوب'
          } else if (data.projectDescription.length < 10) {
            newErrors.projectDescription = 'وصف المشروع يجب أن يكون 10 أحرف على الأقل'
          }

          if (!data.startDate) {
            newErrors.startDate = 'تاريخ البداية مطلوب'
          }

          if (!data.endDate) {
            newErrors.endDate = 'تاريخ النهاية مطلوب'
          } else if (data.startDate && new Date(data.endDate) <= new Date(data.startDate)) {
            newErrors.endDate = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية'
          }
        } else if (formType === 'quick_win') {
          const data = formData as QuickWinData
          if (!data.projectTitle?.trim()) {
            newErrors.projectTitle = 'عنوان المشروع مطلوب'
          } else if (data.projectTitle.length < 5) {
            newErrors.projectTitle = 'عنوان المشروع يجب أن يكون 5 أحرف على الأقل'
          }

          if (!data.section?.trim()) {
            newErrors.section = 'القسم مطلوب'
          }

          if (!data.projectExecutor?.name?.trim()) {
            newErrors['projectExecutor.name'] = 'اسم منفذ المشروع مطلوب'
          }

          if (!data.projectExecutor?.phone?.trim()) {
            newErrors['projectExecutor.phone'] = 'رقم الهاتف مطلوب'
          } else if (!/^05\d{8}$/.test(data.projectExecutor.phone)) {
            newErrors['projectExecutor.phone'] = 'رقم الهاتف يجب أن يبدأ بـ 05 ويكون 10 أرقام'
          }

          if (!data.projectExecutor?.email?.trim()) {
            newErrors['projectExecutor.email'] = 'البريد الإلكتروني مطلوب'
          } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.projectExecutor.email)) {
            newErrors['projectExecutor.email'] = 'البريد الإلكتروني غير صحيح'
          }
        }
        break

      case 'QuickWin':
        if (formType === 'quick_win') {
          const data = formData as QuickWinData
          if (!data.projectTitle?.trim()) {
            newErrors.projectTitle = 'عنوان المشروع مطلوب'
          } else if (data.projectTitle.length <= 5) {
            newErrors.projectTitle = 'عنوان المشروع يجب أن يكون أكثر من 5 أحرف'
          }

          if (!data.section?.trim()) {
            newErrors.section = 'القسم مطلوب'
          }

          if (!data.solution?.description?.trim()) {
            newErrors['solution.description'] = 'وصف الحل مطلوب'
          } else if (data.solution.description.length <= 10) {
            newErrors['solution.description'] = 'وصف الحل يجب أن يكون أكثر من 10 أحرف'
          }

          if (!data.indicatorName?.trim()) {
            newErrors.indicatorName = 'اسم المؤشر مطلوب'
          } else if (data.indicatorName.length <= 3) {
            newErrors.indicatorName = 'اسم المؤشر يجب أن يكون أكثر من 3 أحرف'
          }

          if (data.currentValue === undefined || data.currentValue === null) {
            newErrors.currentValue = 'القيمة الحالية مطلوبة'
          }

          if (data.targetValue === undefined || data.targetValue === null) {
            newErrors.targetValue = 'القيمة المستهدفة مطلوبة'
          }

          if (!data.problemDescription?.trim()) {
            newErrors.problemDescription = 'وصف المشكلة مطلوب'
          } else if (data.problemDescription.length <= 10) {
            newErrors.problemDescription = 'وصف المشكلة يجب أن يكون أكثر من 10 أحرف'
          }
        }
        break

      case 'Find':
        if (!formData.problemDescription?.trim()) {
          newErrors.problemDescription = 'وصف المشكلة مطلوب'
        } else if (formData.problemDescription.length <= 20) {
          newErrors.problemDescription = 'وصف المشكلة يجب أن يكون أكثر من 20 حرف'
        }

        if (!formData.indicatorName?.trim()) {
          newErrors.indicatorName = 'اسم المؤشر مطلوب'
        } else if (formData.indicatorName.length <= 3) {
          newErrors.indicatorName = 'اسم المؤشر يجب أن يكون أكثر من 3 أحرف'
        }

        if (formData.currentValue === undefined || formData.currentValue === null) {
          newErrors.currentValue = 'القيمة الحالية مطلوبة'
        }

        if (formData.targetValue === undefined || formData.targetValue === null) {
          newErrors.targetValue = 'القيمة المستهدفة مطلوبة'
        }

        if (!formData.unit?.trim()) {
          newErrors.unit = 'وحدة القياس مطلوبة'
        }

        if (!formData.improvementDirection) {
          newErrors.improvementDirection = 'اتجاه التحسين مطلوب'
        }
        break

      case 'Organize':
        if (!formData.responsibleDepartment?.trim()) {
          newErrors.responsibleDepartment = 'القسم المسؤول مطلوب'
        }

        if (!formData.teamLeader?.name?.trim()) {
          newErrors['teamLeader.name'] = 'اسم قائد الفريق مطلوب'
        } else if (formData.teamLeader.name.length <= 2) {
          newErrors['teamLeader.name'] = 'اسم قائد الفريق يجب أن يكون أكثر من حرفين'
        }

        if (!formData.teamLeader?.phone?.trim()) {
          newErrors['teamLeader.phone'] = 'رقم هاتف قائد الفريق مطلوب'
        } else if (!/^05\d{8}$/.test(formData.teamLeader.phone)) {
          newErrors['teamLeader.phone'] = 'رقم الهاتف يجب أن يبدأ بـ 05 ويكون 10 أرقام'
        }

        if (!formData.teamLeader?.email?.trim()) {
          newErrors['teamLeader.email'] = 'البريد الإلكتروني لقائد الفريق مطلوب'
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.teamLeader.email)) {
          newErrors['teamLeader.email'] = 'البريد الإلكتروني غير صحيح'
        }
        break

      case 'Clarify':
        if (!formData.processDescription?.trim()) {
          newErrors.processDescription = 'وصف العملية مطلوب'
        } else if (formData.processDescription.length <= 20) {
          newErrors.processDescription = 'وصف العملية يجب أن يكون أكثر من 20 حرف'
        }

        if (!formData.problemScope?.trim()) {
          newErrors.problemScope = 'نطاق المشكلة مطلوب'
        } else if (formData.problemScope.length <= 10) {
          newErrors.problemScope = 'نطاق المشكلة يجب أن يكون أكثر من 10 أحرف'
        }
        break

      case 'Understand':
        if (!formData.rootCause?.trim()) {
          newErrors.rootCause = 'السبب الجذري مطلوب'
        } else if (formData.rootCause.length <= 15) {
          newErrors.rootCause = 'السبب الجذري يجب أن يكون أكثر من 15 حرف'
        }
        break

      case 'Select':
        if (formType === 'enhanced_improvement') {
          const data = formData as EnhancedImprovementData
          if (!data.selectedSolution?.description?.trim()) {
            newErrors['selectedSolution.description'] = 'وصف الحل المختار مطلوب'
          }
        } else if (formType === 'suggestion') {
          const data = formData as SuggestionData
          if (!data.suggestedSolutions || data.suggestedSolutions.length === 0) {
            newErrors.suggestedSolutions = 'يجب إضافة حل واحد على الأقل'
          }
        } else if (formType === 'quick_win') {
          const data = formData as QuickWinData
          if (!data.solution?.description?.trim()) {
            newErrors['solution.description'] = 'وصف الحل مطلوب'
          }
        }
        break

      case 'Planning':
        if (formType === 'enhanced_improvement') {
          const data = formData as EnhancedImprovementData
          if (!data.projectTasks || data.projectTasks.length === 0) {
            newErrors.projectTasks = 'يجب إضافة مهمة واحدة على الأقل'
          }
        }
        break

      case 'RiskManagement':
        // مرحلة اختيارية - لا توجد تحققات إجبارية
        break

      case 'Review':
        // مرحلة المراجعة النهائية - لا توجد تحققات إضافية
        break

      default:
        break
    }

    // تحديث الأخطاء وإرجاع النتيجة
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    const isValid = validateCurrentStep()

    if (isValid && currentStep < totalSteps) {
      // تسجيل الخطوة كمكتملة
      markStepCompleted(currentStep)
      clearStepError(currentStep)
      setCurrentStep(currentStep + 1)
    } else if (!isValid) {
      // تسجيل الخطوة كتحتوي على أخطاء
      markStepWithError(currentStep)

      // عرض رسالة خطأ تفصيلية ومفيدة
      const stepTitle = steps[currentStep - 1]?.title
      const errorDetails = getDetailedErrorMessage(stepTitle, errors)

      // عرض رسالة خطأ محسنة
      showEnhancedErrorDialog(stepTitle, errorDetails)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = () => {
    // التحقق من جميع البيانات قبل الإرسال
    const isAllValid = validateAllData()

    if (isAllValid) {
      onSubmit(formData)
    } else {
      // عرض رسالة خطأ للمستخدم
      alert('يرجى مراجعة البيانات المدخلة وتصحيح الأخطاء قبل الإرسال')
    }
  }

  // دالة التحقق من جميع البيانات
  const validateAllData = (): boolean => {
    setIsValidating(true)
    let isValid = true
    const allErrors: Record<string, string> = {}

    // التحقق من كل خطوة
    for (let i = 1; i <= steps.length; i++) {
      const originalStep = currentStep
      const originalErrors = errors

      // تعيين الخطوة مؤقتاً للتحقق
      setCurrentStep(i)

      // التحقق من الخطوة الحالية
      const stepTitle = steps[i - 1]?.title
      const stepErrors: Record<string, string> = {}

      // نسخ منطق التحقق من validateCurrentStep
      switch (stepTitle) {
        case 'Basic':
          if (formType === 'enhanced_improvement') {
            const data = formData as EnhancedImprovementData
            if (!data.projectName?.trim()) {
              stepErrors.projectName = 'اسم المشروع مطلوب'
            } else if (data.projectName.length < 5) {
              stepErrors.projectName = 'اسم المشروع يجب أن يكون 5 أحرف على الأقل'
            }

            if (!data.projectDescription?.trim()) {
              stepErrors.projectDescription = 'وصف المشروع مطلوب'
            } else if (data.projectDescription.length < 10) {
              stepErrors.projectDescription = 'وصف المشروع يجب أن يكون 10 أحرف على الأقل'
            }
          } else if (formType === 'quick_win') {
            const data = formData as QuickWinData
            if (!data.projectTitle?.trim()) {
              stepErrors.projectTitle = 'عنوان المشروع مطلوب'
            }

            if (!data.section?.trim()) {
              stepErrors.section = 'القسم مطلوب'
            }
          }
          break

        case 'Find':
          if (!formData.problemDescription?.trim()) {
            stepErrors.problemDescription = 'وصف المشكلة مطلوب'
          }

          if (!formData.indicatorName?.trim()) {
            stepErrors.indicatorName = 'اسم المؤشر مطلوب'
          }
          break
      }

      // دمج أخطاء الخطوة مع الأخطاء الإجمالية
      Object.assign(allErrors, stepErrors)

      if (Object.keys(stepErrors).length > 0) {
        isValid = false
      }

      // استعادة الخطوة الأصلية
      setCurrentStep(originalStep)
    }

    // تحديث الأخطاء
    setErrors(allErrors)
    setIsValidating(false)

    return isValid
  }

  const handleSaveDraft = () => {
    // التحقق الأساسي قبل حفظ المسودة
    const hasBasicData = formType === 'enhanced_improvement'
      ? (formData as EnhancedImprovementData).projectName?.trim()
      : formType === 'quick_win'
      ? (formData as QuickWinData).projectTitle?.trim()
      : formData.problemDescription?.trim()

    if (!hasBasicData) {
      alert('يرجى إدخال بعض البيانات الأساسية قبل حفظ المسودة')
      return
    }

    onSaveDraft(formData)
  }

  const renderStepContent = () => {
    const stepTitle = steps[currentStep - 1]?.title

    switch (stepTitle) {
      case 'Basic':
        return (
          <div className="space-y-4">
            <QuickTip
              tip="ابدأ بكتابة عنوان واضح يوضح الهدف من المشروع، ثم اكتب وصف مختصر يشمل المشكلة والحل المقترح."
              className="mb-4"
            />
            <AdaptiveBasicStep
              formType={formType}
              data={formData}
              updateData={updateFormData}
              errors={errors}
            />
          </div>
        )

      case 'QuickWin':
        return (
          <AdaptiveQuickWinStep
            data={formData as QuickWinData}
            updateData={updateFormData}
            errors={errors}
            formType={formType}
          />
        )

      case 'Find':
        return (
          <div className="space-y-4">
            <QuickTip
              tip="اختر مؤشر قابل للقياس ومرتبط مباشرة بالمشكلة. تأكد من دقة القيم الحالية والمستهدفة."
              className="mb-4"
            />
            <ExampleBox
              title="مثال على مؤشر جيد"
              example="نسبة الأخطاء في المخزون: القيمة الحالية 15%، المستهدفة 5%"
              className="mb-4"
            />
            <AdaptiveFindStep
              formType={formType}
              data={formData}
              updateData={updateFormData}
              errors={errors}
            />
          </div>
        )

      case 'Organize':
        return (
          <div className="space-y-4">
            <QuickTip
              tip="اختر قائد فريق لديه الخبرة والوقت الكافي. ضم أعضاء من أقسام مختلفة حسب طبيعة المشروع."
              className="mb-4"
            />
            <AdaptiveOrganizeStep
              formType={formType}
              data={formData}
              updateData={updateFormData}
              errors={errors}
            />
          </div>
        )

      case 'Clarify':
        return (
          <AdaptiveClarifyStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={errors}
          />
        )

      case 'Understand':
        return (
          <div className="space-y-4">
            <WarningBox
              warning="تحليل الأسباب الجذرية مهم جداً لنجاح المشروع. تأكد من الوصول للسبب الحقيقي وليس الأعراض فقط."
              className="mb-4"
            />
            <ExampleBox
              title="مثال على تحليل الأسباب الخمسة"
              example="لماذا تحدث أخطاء المخزون؟ → لا يتم التحديث فوراً → النظام يدوي → لا يوجد تدريب كافي → لا توجد خطة تدريب واضحة"
              className="mb-4"
            />
            <AdaptiveUnderstandStep
              formType={formType}
              data={formData}
              updateData={updateFormData}
              errors={errors}
            />
          </div>
        )

      case 'Select':
        return (
          <AdaptiveSelectStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={errors}
          />
        )

      case 'Planning':
        return (
          <AdaptiveProjectPlanningStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={errors}
          />
        )

      case 'RiskManagement':
        return (
          <AdaptiveRiskManagementStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={errors}
          />
        )

      case 'Review':
        return (
          <AdaptiveReviewStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={errors}
          />
        )

      case 'QuickWin':
        // لم يعد مطلوباً - تم استبداله بالنظام متعدد المراحل
        return <div>مرحلة غير مستخدمة</div>

      default:
        return <div>مرحلة غير معروفة</div>
    }
  }

  const getFormTypeTitle = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'مشروع التحسين الشامل'
      case 'suggestion':
        return 'مقترح تحسيني'
      case 'quick_win':
        return 'كويك وين'
      default:
        return 'نموذج مشروع'
    }
  }

  return (
    <div className="max-w-5xl mx-auto p-4 md:p-6">
      <Card className="bg-white shadow-lg border-0 md:border">
        {/* Header */}
        <div className="border-b border-gray-200 p-4 md:p-6">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-3">
                <h1 className="text-xl md:text-2xl font-bold text-gray-900">
                  {getFormTypeTitle()} - نموذج موحد
                </h1>
                {/* Auto-save indicator */}
                {autoDraft.isAutoSaveEnabled && (
                  <div className="flex items-center gap-1 text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">
                    <CheckCircle className="w-3 h-3" />
                    <span>حفظ تلقائي</span>
                  </div>
                )}
              </div>
              <p className="text-gray-600 mt-1 text-sm md:text-base">
                المرحلة {currentStep} من {totalSteps}: {steps[currentStep - 1]?.description}
              </p>
            </div>
            <div className="flex gap-2 flex-wrap">
              <Button
                onClick={() => setShowStepGuide(true)}
                variant="secondary"
                size="sm"
                className="flex-1 md:flex-none"
              >
                <BookOpen className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">دليل المراحل</span>
                <span className="sm:hidden">دليل</span>
              </Button>
              <Button
                onClick={() => setShowHelpSystem(true)}
                variant="secondary"
                size="sm"
                className="flex-1 md:flex-none"
              >
                <HelpCircle className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">مساعدة</span>
                <span className="sm:hidden">مساعدة</span>
              </Button>


            </div>
          </div>

          {/* Enhanced Progress Bar */}
          <div className="mt-4">
            <div className="w-full mb-4">
              {/* Desktop Progress Bar */}
              <div className="hidden md:block">
                <div className="flex items-center justify-between relative">
                  {steps.map((step, index) => {
                    const isCompleted = completedSteps.includes(step.number)
                    const hasError = stepsWithErrors.includes(step.number)
                    const isCurrent = currentStep === step.number

                    return (
                      <React.Fragment key={step.number}>
                        <div className="flex flex-col items-center relative z-10">
                          <div
                            className={`w-12 h-12 mb-2 cursor-pointer rounded-full flex items-center justify-center font-semibold text-sm transition-all duration-200 border-2 ${
                              hasError
                                ? 'bg-red-600 text-white border-red-600 ring-4 ring-red-100'
                                : isCompleted
                                  ? 'bg-green-600 text-white border-green-600'
                                  : isCurrent
                                    ? 'bg-blue-600 text-white border-blue-600 ring-4 ring-blue-100'
                                    : 'bg-white text-gray-500 border-gray-300'
                            }`}
                            onClick={() => {
                              if (step.number <= currentStep) {
                                setCurrentStep(step.number)
                              }
                            }}
                            title={step.description}
                          >
                            {hasError ? (
                              <AlertCircle className="w-5 h-5" />
                            ) : isCompleted ? (
                              <CheckCircle className="w-5 h-5" />
                            ) : (
                              <span>{step.number}</span>
                            )}
                          </div>

                          <div className="text-center max-w-20">
                            <p className={`text-xs font-medium ${
                              isCurrent
                                ? 'text-blue-900'
                                : isCompleted
                                  ? 'text-green-900'
                                  : hasError
                                    ? 'text-red-900'
                                    : 'text-gray-500'
                            }`}>
                              {step.title}
                            </p>
                          </div>
                        </div>

                        {index < steps.length - 1 && (
                          <div className="flex-1 h-0.5 mx-2 relative -top-6">
                            <div className={`h-full transition-all duration-300 ${
                              isCompleted || isCurrent ? 'bg-green-600' : hasError ? 'bg-red-600' : 'bg-gray-200'
                            }`} />
                          </div>
                        )}
                      </React.Fragment>
                    )
                  })}
                </div>
              </div>

              {/* Mobile Progress Bar */}
              <div className="md:hidden">
                <div className="flex items-center justify-center mb-4">
                  <div className="text-center">
                    <div className={`w-16 h-16 mb-2 rounded-full flex items-center justify-center font-semibold text-sm transition-all duration-200 border-2 ${
                      stepsWithErrors.includes(currentStep)
                        ? 'bg-red-600 text-white border-red-600 ring-4 ring-red-100'
                        : completedSteps.includes(currentStep)
                          ? 'bg-green-600 text-white border-green-600'
                          : 'bg-blue-600 text-white border-blue-600 ring-4 ring-blue-100'
                    }`}>
                      {stepsWithErrors.includes(currentStep) ? (
                        <AlertCircle className="w-5 h-5" />
                      ) : completedSteps.includes(currentStep) ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : (
                        <span>{currentStep}</span>
                      )}
                    </div>
                    <p className="text-sm font-medium text-gray-900">
                      {steps[currentStep - 1]?.title}
                    </p>
                    <p className="text-xs text-gray-500">
                      {currentStep} من {steps.length}
                    </p>
                  </div>
                </div>

                <div className="flex justify-center space-x-2 mb-4">
                  {steps.map((step) => {
                    const isCompleted = completedSteps.includes(step.number)
                    const hasError = stepsWithErrors.includes(step.number)
                    const isCurrent = currentStep === step.number

                    return (
                      <div
                        key={step.number}
                        className={`w-3 h-3 rounded-full transition-all duration-200 ${
                          hasError
                            ? 'bg-red-600'
                            : isCompleted
                              ? 'bg-green-600'
                              : isCurrent
                                ? 'bg-blue-600'
                                : 'bg-gray-200'
                        }`}
                      />
                    )
                  })}
                </div>
              </div>

              {/* Progress Percentage */}
              <div className="mt-4">
                <div className="flex justify-between text-xs text-gray-500 mb-2">
                  <span>التقدم</span>
                  <span>{Math.round(((currentStep - 1) / (steps.length - 1)) * 100)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-blue-600 to-green-600 h-2 rounded-full transition-all duration-500"
                    style={{
                      width: `${((currentStep - 1) / (steps.length - 1)) * 100}%`
                    }}
                  />
                </div>
              </div>

              {/* Step Summary */}
              <div className="mt-4 text-center">
                <div className="flex justify-center space-x-4 text-xs">
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="w-3 h-3 text-green-600" />
                    <span className="text-gray-600">{completedSteps.length} مكتمل</span>
                  </div>
                  {stepsWithErrors.length > 0 && (
                    <div className="flex items-center space-x-1">
                      <AlertCircle className="w-3 h-3 text-red-600" />
                      <span className="text-gray-600">{stepsWithErrors.length} يحتاج مراجعة</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="w-3 h-3 text-gray-400" />
                    <span className="text-gray-600">{steps.length - currentStep} متبقي</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Validation Messages */}
        {Object.keys(errors).length > 0 && (
          <div className="px-4 md:px-6 pt-4">
            <div className="space-y-2 mb-4">
              {Object.entries(errors).map(([field, error]) => (
                <div key={field} className="p-3 rounded-lg border flex items-start gap-2 bg-red-50 border-red-200 text-red-800">
                  <AlertCircle className="w-4 h-4" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">{error}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-4 md:p-6">
          {renderStepContent()}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 md:p-6">
          <div className="flex flex-col md:flex-row md:justify-between gap-4">
            <Button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              variant="secondary"
              className="order-2 md:order-1"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              السابق
            </Button>

            <div className="flex gap-3 order-1 md:order-2">
              <Button
                onClick={handleSaveDraft}
                variant="secondary"
                disabled={isLoading || !autoDraft.hasMinimumData}
                title={!autoDraft.hasMinimumData ? 'يرجى إدخال بعض البيانات أولاً' : 'حفظ التقدم الحالي كمسودة'}
                className="relative"
              >
                <Save className="w-4 h-4 mr-2" />
                حفظ مسودة
                {autoDraft.isAutoSaveEnabled && (
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full"></span>
                )}
              </Button>

              {currentStep < totalSteps ? (
                <Button
                  onClick={handleNext}
                  disabled={!validateCurrentStep() || isLoading}
                  className={`${!validateCurrentStep() ? 'opacity-50 cursor-not-allowed' : ''} relative`}
                  title={!validateCurrentStep() ? 'يرجى إكمال جميع الحقول المطلوبة' : `الانتقال إلى ${steps[currentStep]?.title}`}
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      جاري المعالجة...
                    </>
                  ) : (
                    <>
                      التالي: {steps[currentStep]?.title}
                      <ChevronRight className="w-4 h-4 ml-2" />
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={!validateCurrentStep() || isLoading}
                  className={`bg-green-600 hover:bg-green-700 ${!validateCurrentStep() || isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  title={!validateCurrentStep() ? 'يرجى إكمال جميع الحقول المطلوبة' : 'إرسال الطلب للمراجعة'}
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      جاري الإرسال...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      إرسال الطلب ({completedSteps.length}/{totalSteps} مكتمل)
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Help Systems */}
      {showHelpSystem && (
        <EnhancedHelpSystem 
          onClose={() => setShowHelpSystem(false)} 
          currentStep={currentStep}
        />
      )}
      
      {showStepGuide && (
        <StepByStepGuide 
          currentStep={currentStep}
          onClose={() => setShowStepGuide(false)}
        />
      )}
    </div>
  )
} 