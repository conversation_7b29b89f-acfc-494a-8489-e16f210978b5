'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { SimplifiedProposalForm } from '@/components/forms/simplified/SimplifiedProposalForm'
import { useAuth } from '@/lib/core/auth'
import {
  ChevronLeft,
  MessageSquare,
  CheckCircle,
  Lightbulb,
  FileText,
  Send,
  HelpCircle
} from 'lucide-react'
import { ProgressIndicator, ProposalSteps } from '@/components/ui/ProgressIndicator'
import { InteractiveGuide, useInteractiveGuide, ProposalFormGuide } from '@/components/ui/InteractiveGuide'

export default function NewProposalPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  const router = useRouter()
  const { user } = useAuth()
  const { isOpen, openGuide, closeGuide } = useInteractiveGuide()

  const steps = [
    {
      number: 1,
      title: 'معلومات أساسية',
      description: 'تفاصيل المقترح الأساسية',
      icon: FileText
    },
    {
      number: 2,
      title: 'وصف المشكلة',
      description: 'تحديد المشكلة أو الفرصة',
      icon: MessageSquare
    },
    {
      number: 3,
      title: 'الحل المقترح',
      description: 'اقتراح الحلول والتوصيات',
      icon: Lightbulb
    },
    {
      number: 4,
      title: 'المراجعة والإرسال',
      description: 'مراجعة نهائية وإرسال المقترح',
      icon: Send
    }
  ]

  const handleSubmit = async (data: any) => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      if (!user?.id) {
        setError('يجب تسجيل الدخول أولاً لإرسال المقترح')
        return
      }

      const proposalNumber = generateProposalNumber()
      
      const proposalData = {
        request_number: proposalNumber,
        form_type: 'simplified_proposal',
        form_data: data,
        status: 'submitted',
        created_by: user.id,
        created_at: new Date().toISOString(),
      }

      const response = await fetch('/api/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(proposalData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في إرسال المقترح')
      }

      setSuccess(`تم إرسال المقترح بنجاح! رقم المقترح: ${proposalNumber}. سيتم مراجعة المقترح من قبل أصحاب المصلحة وسيتم إشعارك بالنتيجة قريباً.`)
      
      setTimeout(() => router.push('/requests'), 3000)
      
    } catch (error) {
      console.error('Error submitting proposal:', error)
      setError(error instanceof Error ? error.message : 'حدث خطأ أثناء إرسال المقترح')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveDraft = async (data: any) => {
    try {
      if (!user?.id) {
        throw new Error('يجب تسجيل الدخول أولاً لحفظ المسودة')
      }

      const draftData = {
        form_type: 'simplified_proposal',
        form_data: data,
        user_id: user.id,
      }

      const response = await fetch('/api/requests/drafts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(draftData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في حفظ المسودة')
      }

      alert('تم حفظ المسودة بنجاح!')
    } catch (error) {
      console.error('Error saving draft:', error)
      alert(error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ المسودة')
    }
  }

  const generateProposalNumber = (): string => {
    const year = new Date().getFullYear()
    const month = String(new Date().getMonth() + 1).padStart(2, '0')
    const day = String(new Date().getDate()).padStart(2, '0')
    const time = String(Date.now()).slice(-4)
    
    return `PROP-${year}${month}${day}-${time}`
  }

  return (
    <ProtectedLayout>
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              onClick={() => router.push('/requests/new-simplified')}
              variant="ghost"
              className="flex items-center gap-2"
            >
              <ChevronLeft className="w-4 h-4" />
              العودة للاختيار
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                مقترح جديد
              </h1>
              <p className="text-lg text-gray-600">
                إنشاء مقترح للدراسة والتقييم
              </p>
            </div>
          </div>

          <Button
            onClick={() => openGuide('proposal-form')}
            variant="ghost"
            className="flex items-center gap-2 text-green-600"
          >
            <HelpCircle className="w-4 h-4" />
            دليل الإرشاد
          </Button>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <ProgressIndicator
            steps={ProposalSteps}
            currentStep={currentStep}
            onStepClick={setCurrentStep}
            allowNavigation={true}
            className="max-w-3xl mx-auto"
          />
        </div>

        {/* Error and Success Messages */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-800">{success}</p>
          </div>
        )}

        {/* Form */}
        <Card className="p-8">
          <SimplifiedProposalForm
            currentStep={currentStep}
            onStepChange={setCurrentStep}
            onSubmit={handleSubmit}
            onSaveDraft={handleSaveDraft}
            isLoading={isLoading}
          />
        </Card>

        {/* Help Section */}
        <div className="mt-8 bg-green-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-green-900 mb-3">
            نصائح لكتابة مقترح فعال
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-800">
            <div>
              <h4 className="font-semibold mb-2">وصف المشكلة:</h4>
              <ul className="space-y-1">
                <li>• كن واضح ومحدد</li>
                <li>• استخدم أرقام وإحصائيات</li>
                <li>• اذكر التأثير الحالي</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">الحل المقترح:</h4>
              <ul className="space-y-1">
                <li>• اقترح حلول عملية</li>
                <li>• قدر التكلفة والوقت</li>
                <li>• اذكر الفوائد المتوقعة</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Workflow Info */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            ماذا يحدث بعد إرسال المقترح؟
          </h3>
          <div className="flex items-center justify-between text-sm text-blue-800">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                1
              </div>
              <span>مراجعة أولية</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                2
              </div>
              <span>تقييم الجدوى</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                3
              </div>
              <span>اتخاذ القرار</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                4
              </div>
              <span>تحويل إلى مشروع (إذا تمت الموافقة)</span>
            </div>
          </div>
        </div>

        {/* Interactive Guide */}
        <InteractiveGuide
          title="دليل إنشاء المقترح"
          steps={ProposalFormGuide}
          isOpen={isOpen}
          onClose={closeGuide}
          position="right"
          size="md"
        />
      </div>
    </ProtectedLayout>
  )
}
