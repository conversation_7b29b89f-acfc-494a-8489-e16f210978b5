import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// واجهة بيانات المسودة
interface DraftData {
  form_type: 'enhanced_improvement' | 'suggestion' | 'quick_win'
  form_data: any
  user_id: string
  draft_name?: string
}

// POST - حفظ مسودة جديدة أو تحديث موجودة
export async function POST(request: NextRequest) {
  try {
    const body: DraftData = await request.json()
    
    // التحقق من صحة البيانات
    if (!body.form_type || !body.form_data || !body.user_id) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة غير مكتملة' },
        { status: 400 }
      )
    }

    // تحضير البيانات للحفظ
    const draftData = {
      title: extractDraftTitle(body.form_data, body.form_type),
      description: 'مسودة - ' + (body.form_data.problemDescription || 'طلب مشروع'),
      main_type: 'improvement_project',
      sub_type: body.form_type,
      status: 'draft',
      priority: 'medium',
      requester_id: body.user_id,
      department_id: extractDepartmentId(body.form_data),
      form_data: body.form_data,
      attachments: body.form_data.attachments || []
    }

    // البحث عن مسودة موجودة للمستخدم من نفس النوع
    const { data: existingDraft } = await supabase
      .from('project_requests')
      .select('id')
      .eq('requester_id', body.user_id)
      .eq('sub_type', body.form_type)
      .eq('status', 'draft')
      .single()

    let result
    if (existingDraft) {
      // تحديث المسودة الموجودة
      const { data, error } = await supabase
        .from('project_requests')
        .update(draftData)
        .eq('id', existingDraft.id)
        .select()
        .single()

      if (error) {
        console.error('Database error:', error)
        return NextResponse.json(
          { error: 'حدث خطأ في تحديث المسودة' },
          { status: 500 }
        )
      }
      result = data
    } else {
      // إنشاء مسودة جديدة
      const { data, error } = await supabase
        .from('project_requests')
        .insert([draftData])
        .select()
        .single()

      if (error) {
        console.error('Database error:', error)
        return NextResponse.json(
          { error: 'حدث خطأ في حفظ المسودة' },
          { status: 500 }
        )
      }
      result = data
    }

    return NextResponse.json({
      success: true,
      message: 'تم حفظ المسودة بنجاح',
      draft_id: result.id
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// GET - استرجاع المسودات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const formType = searchParams.get('form_type')

    if (!userId) {
      return NextResponse.json(
        { error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    let query = supabase
      .from('project_requests')
      .select('*')
      .eq('requester_id', userId)
      .eq('status', 'draft')

    if (formType) {
      query = query.eq('sub_type', formType)
    }

    const { data, error } = await query.order('updated_at', { ascending: false })

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في استرجاع المسودات' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: data || []
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مسودة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const draftId = searchParams.get('draft_id')
    const userId = searchParams.get('user_id')

    if (!draftId || !userId) {
      return NextResponse.json(
        { error: 'معرف المسودة والمستخدم مطلوبان' },
        { status: 400 }
      )
    }

    const { error } = await supabase
      .from('project_requests')
      .delete()
      .eq('id', draftId)
      .eq('requester_id', userId)
      .eq('status', 'draft')

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في حذف المسودة' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المسودة بنجاح'
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// دوال مساعدة
function extractDraftTitle(formData: any, formType: string): string {
  let title = ''

  // استخراج العنوان حسب نوع النموذج
  switch (formType) {
    case 'enhanced_improvement':
      title = formData.projectName || 'مشروع تحسين'
      return `[مسودة] ${title} - تحسين شامل`
    case 'quick_win':
      title = formData.projectTitle || 'مشروع سريع'
      return `[مسودة] ${title} - كويك وين`
    case 'suggestion':
      title = formData.problemDescription
        ? formData.problemDescription.substring(0, 50) + '...'
        : 'مقترح تحسين'
      return `[مسودة] ${title} - مقترح`
    default:
      title = formData.projectName || formData.projectTitle || 'طلب مشروع'
      return `[مسودة] ${title}`
  }
}

function extractDepartmentId(formData: any): string {
  // استخراج معرف القسم من البيانات
  if (formData.responsibleDepartment) {
    // إذا كان هناك قسم مسؤول محدد
    return formData.responsibleDepartment
  }

  if (formData.department_id) {
    // إذا كان معرف القسم موجود مباشرة
    return formData.department_id
  }

  // قيمة افتراضية - يجب تحديثها لاحقاً
  return '00000000-0000-0000-0000-000000000000'
}