// مدير الملاحظات والمراجعة التفاعلية
import { supabase } from './supabase';
import {
  SuggestionFeedback,
  NewFeedbackForm,
  SolutionSelection,
  SolutionSelectionForm,
  SuggestionConversion,
  SuggestionConversionData,
  StakeholderInfo,
  ParticipantInfo,
  ParticipatingDepartment,
  InteractiveReviewState,
  InteractiveReviewData
} from '@/types/feedback.types';

export class FeedbackManager {
  /**
   * إرسال المقترح للمراجعة التفاعلية
   */
  static async sendSuggestionForReview(requestId: string): Promise<boolean> {
    try {
      // تحديث حالة الطلب إلى under_feedback
      const { error } = await supabase
        .from('project_requests')
        .update({
          status: 'under_feedback',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (error) throw error;

      // إشعار المشاركين (يمكن إضافة نظام إشعارات لاحقاً)
      console.log(`تم إرسال المقترح ${requestId} للمراجعة التفاعلية`);
      
      return true;
    } catch (error) {
      console.error('خطأ في إرسال المقترح للمراجعة:', error);
      return false;
    }
  }

  /**
   * الحصول على بيانات المراجعة التفاعلية الكاملة مع النموذج
   */
  static async getFullInteractiveReviewData(requestId: string): Promise<InteractiveReviewData | null> {
    try {
      // بيانات تجريبية كاملة للمقترح
      const mockData: InteractiveReviewData = {
        suggestion: {
          id: requestId,
          title: "تطوير نظام إدارة الطلبات الإلكترونية",
          description: "مقترح لتطوير نظام إلكتروني شامل لإدارة طلبات العملاء وتحسين تجربة المستخدم من خلال أتمتة العمليات وتقليل الأخطاء البشرية",
          submittedAt: "2024-01-15T10:30:00Z",
          submittedBy: {
            id: "user-123",
            name: "سارة أحمد العلي",
            department: "تقنية المعلومات",
            role: "محلل أنظمة أول",
            email: "<EMAIL>",
            phone: "966501234567"
          },
          status: "under_review",
          currentPhase: "solution_selection",
          // البيانات الكاملة للنموذج (7 مراحل)
          fullFormData: {
            // المرحلة 1: العثور على المشكلة - Find
            find: {
              problemDescription: "تأخير في معالجة طلبات العملاء يؤدي إلى انخفاض رضا العملاء وزيادة الشكاوى. العملية الحالية تتطلب تدخل يدوي مكثف مما يسبب أخطاء وتأخير في الاستجابة.",
              currentSituation: "يتم استقبال الطلبات عبر البريد الإلكتروني والهاتف، ثم يتم إدخالها يدوياً في نظام Excel، مما يستغرق 2-3 أيام لمعالجة كل طلب. معدل الأخطاء 15% ونسبة رضا العملاء 65%.",
              targetAudience: "العملاء الخارجيون، فريق خدمة العملاء، قسم المبيعات، قسم التقنية"
            },
            
            // المرحلة 2: تنظيم الفريق - Organize
            organize: {
              responsibleDepartment: {
                name: "تقنية المعلومات",
                manager: "فاطمة علي الزهراني",
                managerPhone: "966501111111"
              },
              teamLeader: {
                name: "أحمد محمد الأحمدي",
                phone: "966502222222",
                email: "<EMAIL>"
              },
              participatingDepartments: [
                {
                  name: "خدمة العملاء",
                  manager: {
                    name: "سالم أحمد الغامدي",
                    phone: "966503333333",
                    email: "<EMAIL>"
                  },
                  involvement: "تحديد المتطلبات وتدريب المستخدمين"
                },
                {
                  name: "الجودة والتطوير",
                  manager: {
                    name: "نورا خالد السلمي",
                    phone: "966504444444",
                    email: "<EMAIL>"
                  },
                  involvement: "مراجعة العمليات ووضع معايير الجودة"
                },
                {
                  name: "المبيعات",
                  manager: {
                    name: "محمد عبدالله القحطاني",
                    phone: "966505555555",
                    email: "<EMAIL>"
                  },
                  involvement: "تحديد احتياجات العملاء ومتطلبات التقارير"
                }
              ]
            },
            
            // المرحلة 3: توضيح العمليات - Clarify
            clarify: {
              processDescription: "العملية الحالية تبدأ باستقبال الطلب عبر البريد الإلكتروني أو الهاتف، ثم يقوم موظف خدمة العملاء بإدخال البيانات في ملف Excel، يتم مراجعة الطلب من قبل المشرف، ثم إرساله لقسم المبيعات للمعالجة، وأخيراً إرسال رد للعميل. هذه العملية تستغرق 2-3 أيام عمل وتتضمن 6 خطوات يدوية.",
              processMap: "تم إرفاق خريطة العملية الحالية"
            },
            
            // المرحلة 4: فهم أسباب المشكلة - Understand
            understand: {
              analysisMethod: "whys_5",
              rootCause: "عدم وجود نظام إلكتروني موحد لإدارة طلبات العملاء، مما يؤدي إلى الاعتماد على العمليات اليدوية والأنظمة المنفصلة التي تفتقر للتكامل والأتمتة.",
              analysisDetails: "تحليل الأسباب الجذرية باستخدام خمسة أسئلة لماذا كشف أن المشكلة الأساسية تكمن في عدم وجود استثمار كافٍ في تقنية المعلومات وعدم وضع استراتيجية رقمية واضحة للمؤسسة."
            },
            
            // المرحلة 5: اقتراح الحلول - Select
            select: {
              proposedSolutions: [
                {
                  id: "solution-1",
                  title: "تطوير نظام مخصص داخلياً",
                  description: "بناء نظام إدارة طلبات مخصص باستخدام فريق التطوير الداخلي مع قاعدة بيانات مركزية وواجهات ويب وموبايل",
                  expectedBenefits: [
                    "تحكم كامل في المواصفات والتطوير",
                    "إمكانية التخصيص حسب الحاجة",
                    "عدم الاعتماد على جهات خارجية",
                    "أمان عالي للبيانات"
                  ],
                  implementationSteps: [
                    "تحليل المتطلبات التفصيلية",
                    "تصميم قاعدة البيانات والواجهات",
                    "تطوير النظام على مراحل",
                    "اختبار شامل للنظام",
                    "تدريب المستخدمين والتشغيل التجريبي",
                    "الإطلاق الرسمي والدعم الفني"
                  ],
                  feasibilityScore: 75
                },
                {
                  id: "solution-2", 
                  title: "شراء نظام جاهز من مورد خارجي",
                  description: "اقتناء نظام إدارة علاقات العملاء (CRM) جاهز من شركة متخصصة مع إمكانيات التخصيص والتكامل",
                  expectedBenefits: [
                    "سرعة في التنفيذ",
                    "تكلفة أقل في البداية",
                    "دعم فني مستمر من المورد",
                    "تحديثات وتطويرات دورية"
                  ],
                  implementationSteps: [
                    "دراسة الأنظمة المتاحة في السوق",
                    "طلب عروض أسعار من الموردين",
                    "تقييم الأنظمة واختيار الأنسب",
                    "التفاوض وتوقيع العقد",
                    "تخصيص النظام وتكامله",
                    "التدريب والتشغيل"
                  ],
                  feasibilityScore: 85
                },
                {
                  id: "solution-3",
                  title: "حل مختلط (تطوير + شراء)",
                  description: "استخدام منصة جاهزة كأساس مع تطوير مكونات مخصصة للاحتياجات الخاصة",
                  expectedBenefits: [
                    "توازن بين السرعة والتخصيص",
                    "استفادة من المزايا الجاهزة",
                    "مرونة في التطوير المستقبلي",
                    "تقليل المخاطر التقنية"
                  ],
                  implementationSteps: [
                    "اختيار المنصة الأساسية المناسبة",
                    "تحديد المكونات المطلوب تطويرها",
                    "تطوير الواجهات والتكاملات المخصصة",
                    "اختبار التكامل بين الأنظمة",
                    "التدريب والتشغيل التدريجي",
                    "التحسين المستمر"
                  ],
                  feasibilityScore: 90
                }
              ]
            },
            
            // المرحلة 6: التوصيات النهائية - Recommendations
            recommendations: {
              finalRecommendations: "بناءً على التحليل المفصل، أوصي بتبني الحل المختلط (الحل الثالث) كونه يوفر أفضل توازن بين السرعة والتخصيص والتكلفة. يمكن البدء بمنصة CRM جاهزة مثل Salesforce أو Microsoft Dynamics، ثم تطوير المكونات المخصصة تدريجياً حسب الحاجة.",
              implementationTips: [
                "البدء بمرحلة تجريبية مع قسم واحد",
                "التأكد من تدريب جميع المستخدمين قبل الإطلاق",
                "وضع خطة للنسخ الاحتياطي والاستمرارية",
                "تحديد مؤشرات الأداء لقياس نجاح النظام",
                "إشراك المستخدمين في عملية التطوير والاختبار"
              ]
            },
            
            // المؤشر المستهدف
            kpi: {
              name: "متوسط وقت معالجة طلبات العملاء",
              currentValue: 3,
              targetValue: 0.5,
              unit: "يوم",
              improvementDirection: "decrease",
              improvementPercentage: 83
            }
          }
        },
        participants: [
          {
            id: 'participant-1',
            name: 'أحمد محمد',
            role: 'team_leader',
            email: '<EMAIL>',
            phone: '+966501234567',
            department: 'تطوير الأنظمة',
            position: 'قائد فريق التطوير'
          },
          {
            id: 'participant-2', 
            name: 'فاطمة علي',
            role: 'department_manager',
            email: '<EMAIL>',
            phone: '+966507654321',
            department: 'تطوير الأنظمة',
            position: 'مدير قسم تطوير الأنظمة'
          },
          {
            id: 'participant-3',
            name: 'سالم أحمد',
            role: 'participating_department_manager',
            email: '<EMAIL>',
            phone: '+966509876543',
            department: 'تقنية المعلومات',
            position: 'مدير قسم تقنية المعلومات'
          },
          {
            id: 'participant-4',
            name: 'نورا خالد',
            role: 'participating_department_manager',
            email: '<EMAIL>',
            phone: '+966505432109',
            department: 'الجودة والتطوير',
            position: 'مدير قسم الجودة والتطوير'
          }
        ],
        participatingDepartments: [
          {
            id: 'dept-1',
            name: 'تطوير الأنظمة',
            manager: {
              name: 'فاطمة علي',
              phone: '+966507654321',
              email: '<EMAIL>',
              position: 'مدير قسم تطوير الأنظمة'
            },
            involvement: 'primary',
            responsibilities: ['الإشراف الرئيسي على المشروع', 'تنفيذ الحلول المقترحة']
          },
          {
            id: 'dept-2',
            name: 'تقنية المعلومات',
            manager: {
              name: 'سالم أحمد',
              phone: '+966509876543',
              email: '<EMAIL>',
              position: 'مدير قسم تقنية المعلومات'
            },
            involvement: 'secondary',
            responsibilities: ['دعم البنية التحتية', 'التكامل مع الأنظمة الموجودة']
          },
          {
            id: 'dept-3',
            name: 'الجودة والتطوير',
            manager: {
              name: 'نورا خالد',
              phone: '+966505432109',
              email: '<EMAIL>',
              position: 'مدير قسم الجودة والتطوير'
            },
            involvement: 'supporting',
            responsibilities: ['مراجعة معايير الجودة', 'ضمان التوافق مع المعايير']
          }
        ],
        feedbacks: [],
        solutionEvaluations: [],
        statistics: {
          totalFeedbacks: 0,
          byType: {
            approval: 0,
            question: 0,
            suggestion: 0,
            concern: 0,
            improvement: 0,
            rejection: 0
          },
          byPriority: {
            high: 0,
            medium: 0,
            low: 0,
            critical: 0
          },
          byStatus: {
            pending: 0,
            addressed: 0,
            resolved: 0,
            dismissed: 0
          },
          byReviewerType: {
            team_leader: 0,
            department_manager: 0,
            participating_department_manager: 0,
            pmo_stakeholder: 0
          }
        },
        currentPhase: 'feedback_collection'
      };

      return mockData;
    } catch (error) {
      console.error('خطأ في الحصول على بيانات المراجعة الكاملة:', error);
      return null;
    }
  }

  /**
   * الحصول على بيانات المراجعة التفاعلية
   */
  static async getInteractiveReviewData(requestId: string): Promise<InteractiveReviewState | null> {
    try {
      // الحصول على بيانات الطلب
      const { data: request, error: requestError } = await supabase
        .from('project_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      // إذا لم يتم العثور على المقترح، إنشاء بيانات تجريبية للاختبار
      if (requestError || !request) {
        console.log('لم يتم العثور على المقترح، استخدام بيانات تجريبية للاختبار');
        
        // بيانات تجريبية للمشاركين (بدون أعضاء الفريق)
        const mockParticipants: ParticipantInfo[] = [
          {
            id: 'participant-1',
            name: 'أحمد محمد',
            role: 'team_leader',
            email: '<EMAIL>',
            phone: '+966501234567',
            department: 'تطوير الأنظمة',
            position: 'قائد فريق التطوير'
          },
          {
            id: 'participant-2', 
            name: 'فاطمة علي',
            role: 'department_manager',
            email: '<EMAIL>',
            phone: '+966507654321',
            department: 'تطوير الأنظمة',
            position: 'مدير قسم تطوير الأنظمة'
          },
          {
            id: 'participant-3',
            name: 'سالم أحمد',
            role: 'participating_department_manager',
            email: '<EMAIL>',
            phone: '+966509876543',
            department: 'تقنية المعلومات',
            position: 'مدير قسم تقنية المعلومات'
          },
          {
            id: 'participant-4',
            name: 'نورا خالد',
            role: 'participating_department_manager',
            email: '<EMAIL>',
            phone: '+966505432109',
            department: 'الجودة والتطوير',
            position: 'مدير قسم الجودة والتطوير'
          }
        ];

        // بيانات تجريبية للأقسام المشاركة
        const mockParticipatingDepartments: ParticipatingDepartment[] = [
          {
            id: 'dept-1',
            name: 'تطوير الأنظمة',
            manager: {
              name: 'فاطمة علي',
              phone: '+966507654321',
              email: '<EMAIL>',
              position: 'مدير قسم تطوير الأنظمة'
            },
            involvement: 'primary',
            responsibilities: ['الإشراف الرئيسي على المشروع', 'تنفيذ الحلول المقترحة']
          },
          {
            id: 'dept-2',
            name: 'تقنية المعلومات',
            manager: {
              name: 'سالم أحمد',
              phone: '+966509876543',
              email: '<EMAIL>',
              position: 'مدير قسم تقنية المعلومات'
            },
            involvement: 'secondary',
            responsibilities: ['دعم البنية التحتية', 'التكامل مع الأنظمة الموجودة']
          },
          {
            id: 'dept-3',
            name: 'الجودة والتطوير',
            manager: {
              name: 'نورا خالد',
              phone: '+966505432109',
              email: '<EMAIL>',
              position: 'مدير قسم الجودة والتطوير'
            },
            involvement: 'supporting',
            responsibilities: ['مراجعة معايير الجودة', 'ضمان التوافق مع المعايير']
          }
        ];

        const mockFeedbacks: SuggestionFeedback[] = [
          {
            id: 'feedback-1',
            requestId,
            reviewerId: 'participant-1',
            reviewerName: 'أحمد محمد',
            reviewerType: 'team_leader',
            solutionId: 'solution-1',
            comment: 'المقترح ممتاز ويحل مشكلة حقيقية نواجهها يومياً. أقترح البدء في التنفيذ فوراً.',
            feedbackType: 'approval',
            priority: 'high',
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 'participant-1'
          },
          {
            id: 'feedback-2',
            requestId,
            reviewerId: 'participant-2',
            reviewerName: 'فاطمة علي',
            reviewerType: 'department_manager',
            solutionId: 'solution-1',
            comment: 'الفكرة جيدة ولكن أحتاج توضيح أكثر حول التكلفة المتوقعة والموارد المطلوبة.',
            feedbackType: 'question',
            priority: 'medium',
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 'participant-2'
          },
          {
            id: 'feedback-3',
            requestId,
            reviewerId: 'participant-3',
            reviewerName: 'سالم أحمد',
            reviewerType: 'participating_department_manager',
            solutionId: 'solution-1',
            comment: 'من ناحية تقنية المعلومات، المقترح قابل للتنفيذ. سنحتاج لتحديث البنية التحتية.',
            feedbackType: 'suggestion',
            priority: 'medium',
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 'participant-3'
          },
          {
            id: 'feedback-4',
            requestId,
            reviewerId: 'participant-4',
            reviewerName: 'نورا خالد',
            reviewerType: 'participating_department_manager',
            solutionId: 'solution-1',
            comment: 'يجب ضمان التوافق مع معايير الجودة المعتمدة. أقترح إضافة مرحلة اختبار شاملة.',
            feedbackType: 'improvement',
            priority: 'high',
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 'participant-4'
          }
        ];

        return {
          requestId,
          currentPhase: 'feedback_collection',
          participants: mockParticipants,
          participatingDepartments: mockParticipatingDepartments,
          feedbacks: mockFeedbacks,
          solutionEvaluations: [
            {
              solutionId: 'solution-1',
              feedbacks: mockFeedbacks,
              consensusLevel: 'high',
              recommendationLevel: 'highly_recommended'
            }
          ],
          statistics: {
            totalFeedbacks: mockFeedbacks.length,
            byType: {
              approval: 1,
              question: 1,
              suggestion: 1,
              concern: 0,
              improvement: 1,
              rejection: 0
            },
            byPriority: {
              high: 2,
              medium: 2,
              low: 0,
              critical: 0
            },
            byStatus: {
              pending: 4,
              addressed: 0,
              resolved: 0,
              dismissed: 0
            },
            byReviewerType: {
              team_leader: 1,
              department_manager: 1,
              participating_department_manager: 2,
              pmo_stakeholder: 0
            }
          },
          // للتوافق مع النظام القديم
          stakeholders: mockParticipants
        };
      }

      // استخراج المشاركين والأقسام المشاركة من form_data
      const participants = request.form_data?.teamMembers || [];
      const participatingDepartments = request.form_data?.participatingDepartments || [];

      // الحصول على الملاحظات الموجودة
      const { data: feedbacks, error: feedbackError } = await supabase
        .from('suggestion_feedback')
        .select('*')
        .eq('request_id', requestId)
        .order('created_at', { ascending: false });

      if (feedbackError) throw feedbackError;

      // تحويل البيانات إلى النوع المطلوب
      const formattedFeedbacks: SuggestionFeedback[] = (feedbacks || []).map(feedback => ({
        id: feedback.id,
        requestId: feedback.request_id,
        reviewerId: feedback.reviewer_id,
        reviewerName: feedback.reviewer_name,
        reviewerType: feedback.reviewer_type,
        solutionId: feedback.solution_id,
        comment: feedback.comment,
        feedbackType: feedback.feedback_type,
        priority: feedback.priority,
        status: feedback.status,
        createdAt: feedback.created_at,
        updatedAt: feedback.updated_at,
        createdBy: feedback.created_by
      }));

      // حساب الإحصائيات
      const statistics = this.calculateFeedbackStatistics(formattedFeedbacks);

      return {
        requestId,
        currentPhase: 'feedback_collection',
        participants,
        participatingDepartments,
        feedbacks: formattedFeedbacks,
        solutionEvaluations: [],
        statistics,
        // للتوافق مع النظام القديم
        stakeholders: participants
      };

    } catch (error) {
      console.error('خطأ في الحصول على بيانات المراجعة التفاعلية:', error);
      return null;
    }
  }

  /**
   * حساب إحصائيات الملاحظات
   */
  private static calculateFeedbackStatistics(feedbacks: SuggestionFeedback[]) {
    const statistics = {
      totalFeedbacks: feedbacks.length,
      byType: {
        approval: 0,
        question: 0,
        suggestion: 0,
        concern: 0,
        improvement: 0,
        rejection: 0
      },
      byPriority: {
        high: 0,
        medium: 0,
        low: 0,
        critical: 0
      },
      byStatus: {
        pending: 0,
        addressed: 0,
        resolved: 0,
        dismissed: 0
      },
      byReviewerType: {
        team_leader: 0,
        department_manager: 0,
        participating_department_manager: 0,
        pmo_stakeholder: 0
      }
    };

    feedbacks.forEach(feedback => {
      statistics.byType[feedback.feedbackType]++;
      statistics.byPriority[feedback.priority]++;
      statistics.byStatus[feedback.status]++;
      statistics.byReviewerType[feedback.reviewerType]++;
    });

    return statistics;
  }

  /**
   * إضافة ملاحظة جديدة
   */
  static async addFeedback(
    requestId: string,
    feedback: NewFeedbackForm,
    reviewerInfo: {
      id: string;
      name: string;
      type: string;
    }
  ): Promise<SuggestionFeedback | null> {
    try {
      // تحديد نوع المراجع
      let reviewerType = 'pmo_stakeholder';
      if (reviewerInfo.type === 'team_leader') reviewerType = 'team_leader';
      else if (reviewerInfo.type === 'department_manager') reviewerType = 'department_manager';
      else if (reviewerInfo.type === 'team_member') reviewerType = 'team_member';

      const { data, error } = await supabase
        .from('suggestion_feedback')
        .insert({
          request_id: requestId,
          reviewer_id: reviewerInfo.id,
          reviewer_name: reviewerInfo.name,
          reviewer_type: reviewerType,
          solution_id: feedback.solutionId,
          comment: feedback.comment,
          feedback_type: feedback.feedbackType,
          priority: feedback.priority,
          status: 'pending',
          created_by: reviewerInfo.id
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        requestId: data.request_id,
        reviewerId: data.reviewer_id,
        reviewerName: data.reviewer_name,
        reviewerType: data.reviewer_type,
        solutionId: data.solution_id,
        comment: data.comment,
        feedbackType: data.feedback_type,
        priority: data.priority,
        status: data.status,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        createdBy: data.created_by
      };
    } catch (error) {
      console.error('خطأ في إضافة الملاحظة:', error);
      return null;
    }
  }

  /**
   * حفظ اختيار الحل الأمثل
   */
  static async saveSolutionSelection(
    requestId: string,
    selection: SolutionSelectionForm,
    selectedBy: string
  ): Promise<SolutionSelection | null> {
    try {
      // للاختبار: نجح دائماً مع البيانات التجريبية
      console.log('حفظ اختيار الحل:', { requestId, selection, selectedBy });
      
      // محاكاة حفظ البيانات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // إرجاع بيانات تجريبية
      const mockSelection: SolutionSelection = {
        id: `selection_${Date.now()}`,
        requestId,
        selectedSolutionId: selection.selectedSolutionId,
        selectionRationale: selection.selectionRationale,
        participantConsensus: selection.participantApprovals.map(approval => ({
          participantId: approval.participantId,
          participantName: 'اسم المشارك',
          role: 'team_leader',
          approval: approval.approval,
          conditions: approval.conditions,
          timestamp: new Date().toISOString()
        })),
        selectedBy,
        selectedAt: new Date().toISOString()
      };
      
      return mockSelection;
      
      // الكود الأصلي للاستخدام مع قاعدة البيانات الحقيقية:
      /*
      // تحويل موافقات أصحاب المصلحة إلى النوع المطلوب
      const stakeholderConsensus = selection.stakeholderApprovals.map(approval => ({
        stakeholderId: approval.stakeholderId,
        stakeholderName: '', // سيتم ملؤه من البيانات
        role: 'team_member' as const, // سيتم تحديثه
        approval: approval.approval,
        conditions: approval.conditions,
        timestamp: new Date().toISOString()
      }));

      const { data, error } = await supabase
        .from('solution_selection')
        .insert({
          request_id: requestId,
          selected_solution_id: selection.selectedSolutionId,
          selection_rationale: selection.selectionRationale,
          stakeholder_consensus: stakeholderConsensus,
          selected_by: selectedBy
        })
        .select()
        .single();

      if (error) throw error;

      // تحديث حالة الطلب
      await supabase
        .from('project_requests')
        .update({
          status: 'feedback_completed',
          selected_solution_id: selection.selectedSolutionId,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      return {
        id: data.id,
        requestId: data.request_id,
        selectedSolutionId: data.selected_solution_id,
        selectionRationale: data.selection_rationale,
        stakeholderConsensus: data.stakeholder_consensus,
        selectedBy: data.selected_by,
        selectedAt: data.selected_at
      };
      */
    } catch (error) {
      console.error('خطأ في حفظ اختيار الحل:', error);
      return null;
    }
  }

  /**
   * تحويل المقترح إلى مشروع
   */
  static async convertSuggestionToProject(
    originalRequestId: string,
    conversionData: SuggestionConversionData,
    convertedBy: string
  ): Promise<{ success: boolean; newRequestId?: string }> {
    try {
      // للاختبار: نجح دائماً مع البيانات التجريبية
      console.log('تحويل المقترح إلى مشروع:', { originalRequestId, conversionData, convertedBy });
      
      // محاكاة عملية التحويل
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // إنشاء رقم طلب مشروع جديد
      const year = new Date().getFullYear();
      const month = String(new Date().getMonth() + 1).padStart(2, '0');
      const day = String(new Date().getDate()).padStart(2, '0');
      const time = String(Date.now()).slice(-4);
      const newRequestId = `REQ-${year}${month}${day}-${time}`;
      
      // حفظ البيانات في localStorage للاختبار
      const convertedProject = {
        id: newRequestId,
        originalSuggestionId: originalRequestId,
        ...conversionData,
        convertedBy,
        convertedAt: new Date().toISOString(),
        status: 'draft',
        type: 'converted_project'
      };
      
      // حفظ في localStorage
      const existingProjects = JSON.parse(localStorage.getItem('convertedProjects') || '[]');
      existingProjects.push(convertedProject);
      localStorage.setItem('convertedProjects', JSON.stringify(existingProjects));
      
      return {
        success: true,
        newRequestId
      };
      
      // الكود الأصلي للاستخدام مع قاعدة البيانات الحقيقية:
      /*
      // الحصول على البيانات الأصلية
      const { data: originalRequest, error: fetchError } = await supabase
        .from('project_requests')
        .select('*')
        .eq('id', originalRequestId)
        .single();

      if (fetchError) throw fetchError;

      // إنشاء طلب مشروع جديد
      const newProjectData = {
        ...originalRequest.form_data,
        // إضافة البيانات الجديدة
        projectName: conversionData.projectName,
        projectDescription: conversionData.projectDescription,
        startDate: conversionData.startDate,
        endDate: conversionData.endDate,
        priority: conversionData.priority,
        selectedSolution: conversionData.selectedSolution,
        projectTasks: conversionData.projectTasks,
        requiredResources: conversionData.requiredResources,
        risks: conversionData.risks
      };

      const { data: newRequest, error: createError } = await supabase
        .from('project_requests')
        .insert({
          request_type: 'project',
          main_type: 'improvement',
          sub_type: 'comprehensive', // تحويل إلى مشروع تحسين شامل
          form_data: newProjectData,
          status: 'draft',
          created_by: convertedBy,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) throw createError;

      // حفظ بيانات التحويل
      await supabase
        .from('suggestion_conversion')
        .insert({
          original_request_id: originalRequestId,
          new_request_id: newRequest.id,
          conversion_data: conversionData,
          converted_by: convertedBy
        });

      // تحديث الطلب الأصلي
      await supabase
        .from('project_requests')
        .update({
          status: 'converting_to_project',
          updated_at: new Date().toISOString()
        })
        .eq('id', originalRequestId);

      return {
        success: true,
        newRequestId: newRequest.id
      };
      */
    } catch (error) {
      console.error('خطأ في تحويل المقترح إلى مشروع:', error);
      return { success: false };
    }
  }

  /**
   * الحصول على تاريخ التحويل
   */
  static async getConversionHistory(requestId: string): Promise<SuggestionConversion[]> {
    try {
      const { data, error } = await supabase
        .from('suggestion_conversion')
        .select('*')
        .or(`original_request_id.eq.${requestId},new_request_id.eq.${requestId}`)
        .order('converted_at', { ascending: false });

      if (error) throw error;

      return (data || []).map(item => ({
        id: item.id,
        originalRequestId: item.original_request_id,
        newRequestId: item.new_request_id,
        conversionData: item.conversion_data,
        convertedBy: item.converted_by,
        convertedAt: item.converted_at
      }));
    } catch (error) {
      console.error('خطأ في الحصول على تاريخ التحويل:', error);
      return [];
    }
  }

  /**
   * تحديث حالة الملاحظة
   */
  static async updateFeedbackStatus(
    feedbackId: string,
    status: 'pending' | 'addressed' | 'resolved' | 'dismissed'
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('suggestion_feedback')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', feedbackId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('خطأ في تحديث حالة الملاحظة:', error);
      return false;
    }
  }

  /**
   * حذف ملاحظة
   */
  static async deleteFeedback(feedbackId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('suggestion_feedback')
        .delete()
        .eq('id', feedbackId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('خطأ في حذف الملاحظة:', error);
      return false;
    }
  }

  /**
   * الحصول على إحصائيات المراجعة
   */
  static async getReviewStatistics(requestId: string): Promise<{
    totalFeedbacks: number;
    stakeholdersParticipated: number;
    averageResponseTime: number;
    consensusLevel: 'high' | 'medium' | 'low';
  }> {
    try {
      const { data: feedbacks, error } = await supabase
        .from('suggestion_feedback')
        .select('reviewer_id, created_at')
        .eq('request_id', requestId);

      if (error) throw error;

      const uniqueReviewers = new Set(feedbacks?.map(f => f.reviewer_id) || []).size;
      const totalFeedbacks = feedbacks?.length || 0;

      // حساب متوسط وقت الاستجابة (مبسط)
      const averageResponseTime = 0; // يمكن تطويره لاحقاً

      // تحديد مستوى الإجماع
      let consensusLevel: 'high' | 'medium' | 'low' = 'low';
      if (uniqueReviewers >= 3) consensusLevel = 'high';
      else if (uniqueReviewers >= 2) consensusLevel = 'medium';

      return {
        totalFeedbacks,
        stakeholdersParticipated: uniqueReviewers,
        averageResponseTime,
        consensusLevel
      };
    } catch (error) {
      console.error('خطأ في الحصول على إحصائيات المراجعة:', error);
      return {
        totalFeedbacks: 0,
        stakeholdersParticipated: 0,
        averageResponseTime: 0,
        consensusLevel: 'low'
      };
    }
  }
} 